# -*- coding: utf-8 -*-
"""
短信发送记录序列化器
"""
from rest_framework import serializers
from apps.outbound_communication.models import SmsRecord


class SmsRecordListSerializer(serializers.ModelSerializer):
    """短信发送记录列表序列化器"""

    # 发送状态中文显示
    sms_status_cn = serializers.CharField(source="get_sms_status_display", read_only=True)

    # 短信类型中文显示
    sms_type_cn = serializers.CharField(source="get_sms_type_display", read_only=True)

    # 调解案件号显示
    mediation_case_number = serializers.CharField(source="mediation_case.case_number", read_only=True)

    # 发送时间格式化显示
    send_time = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)

    # 送达时间格式化显示
    delivery_time = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)

    # 短信内容长度统计
    content_length = serializers.SerializerMethodField()

    # 短信内容预览 - 截取前50个字符用于列表显示
    content_preview = serializers.SerializerMethodField()

    class Meta:
        model = SmsRecord
        fields = [
            "id",
            "recipient_phone",
            "sms_content",
            "content_preview",
            "content_length",
            "sms_status",
            "sms_status_cn",
            "sms_type",
            "sms_type_cn",
            "send_time",
            "delivery_time",
            "task_batch_id",
            "mediation_case",
            "mediation_case_number",
            "failure_reason",
        ]

    def get_content_length(self, obj):
        """获取短信内容长度"""
        if not obj.sms_content:
            return 0
        return len(obj.sms_content)

    def get_content_preview(self, obj):
        """获取短信内容预览 - 截取前50个字符用于列表显示"""
        if not obj.sms_content:
            return "无内容"

        if len(obj.sms_content) <= 50:
            return obj.sms_content
        else:
            return obj.sms_content[:50] + "..."
