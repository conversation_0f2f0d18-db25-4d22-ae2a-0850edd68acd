#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : tasks.py
<AUTHOR> JT_DA
@Date     : 2025/07/29
@File_Desc: 调解管理任务函数，包含异步任务和PDF生成等业务逻辑封装
"""

import logging
import os
from io import BytesIO
from typing import Dict, List, Tuple, Optional

# 延迟导入Django相关模块，避免在文件加载时就需要Django环境
try:
    # 尝试导入Celery相关模块（在Django环境中）
    from ops_management.celery import app, CeleryResult

    CELERY_AVAILABLE = True
except ImportError:
    # 如果无法导入，说明Django环境未初始化，设置标志位
    CELERY_AVAILABLE = False
    app = None
    CeleryResult = None

# 获取日志记录器
logger = logging.getLogger(__name__)


# 创建装饰器函数，支持条件装饰
def conditional_task_decorator(func):
    """条件任务装饰器，只在Celery可用时应用@app.task装饰器"""
    if CELERY_AVAILABLE and app:
        return app.task()(func)
    return func


def generate_mediation_agreement_pdf(mediation_case) -> Tuple[bool, Optional[object], str]:
    """
    生成调解协议PDF并管理文件存储的统一函数

    该函数封装了PDF生成和文件管理的完整流程，包括：
    1. 生成PDF文件内容
    2. 创建MediationCaseFile实例来存储PDF文件
    3. 清理该调解案件已有的同名PDF文件记录
    4. 将文件关联到调解案件的附件中

    Args:
        mediation_case: 调解案件实例，必须包含confirmed_mediation_config和confirmed_plan_config

    Returns:
        Tuple[bool, Optional[MediationCaseFile], str]:
        - success: 是否成功生成PDF
        - file_instance: 成功时返回MediationCaseFile实例，失败时为None
        - error_msg: 错误信息，成功时为空字符串

    Example:
        success, file_obj, error = generate_mediation_agreement_pdf(mediation_case)
        if success:
            download_url = FileSecurityHelper.generate_secure_download_url(file_obj)
        else:
            logger.error(f"PDF生成失败: {error}")
    """
    try:
        # 延迟导入Django模块，确保在函数调用时Django环境已初始化
        from django.core.files.base import ContentFile
        from apps.mediation_management.models import MediationCaseFile

        logger.info(f"开始生成调解协议PDF，案件号: {mediation_case.case_number}")

        # 验证必要的数据是否存在
        if not mediation_case.confirmed_mediation_config and not mediation_case.confirmed_plan_config:
            error_msg = f"调解案件 {mediation_case.case_number} 缺少确认的配置信息"
            logger.warning(error_msg)
            return False, None, error_msg

        # 生成PDF文件内容
        pdf_content = _generate_pdf_content(mediation_case)
        if not pdf_content:
            error_msg = f"PDF内容生成失败，案件号: {mediation_case.case_number}"
            logger.error(error_msg)
            return False, None, error_msg

        # 创建文件名
        file_name = f"调解协议_{mediation_case.case_number}.pdf"

        # 清理该调解案件已有的同名PDF文件记录
        _cleanup_existing_pdf_files(mediation_case, file_name)

        # 创建MediationCaseFile对象
        case_file = MediationCaseFile(file_name=file_name)
        case_file.file.save(file_name, ContentFile(pdf_content), save=False)
        case_file.save()

        # 将文件关联到调解案件的附件中
        mediation_case.attachments.add(case_file)

        logger.info(f"调解协议PDF生成成功，案件号: {mediation_case.case_number}, 文件大小: {len(pdf_content)} bytes")
        return True, case_file, ""

    except Exception as e:
        error_msg = f"生成调解协议PDF时发生异常: {str(e)}"
        logger.error(error_msg)
        return False, None, error_msg


def _generate_pdf_content(mediation_case):
    """
    生成调解协议PDF内容的内部函数

    根据调解案件对象生成标准化的调解协议PDF文件内容，包含标题、案件信息、
    调解信息、调解方案和电子签名等完整内容。支持中文字体显示。

    Args:
        mediation_case: 调解案件对象

    Returns:
        bytes: PDF文件的二进制内容，失败时返回None
    """
    try:
        # 延迟导入ReportLab相关模块
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
        from utils.expression_calculator import calculate_expression_with_asset_data

        # 创建内存缓冲区
        buffer = BytesIO()

        # 创建PDF文档
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=72)

        # 注册中文字体
        font_name = _register_chinese_font()

        # 获取样式表并创建自定义样式
        styles = getSampleStyleSheet()

        # 标题样式
        title_style = ParagraphStyle(
            "CustomTitle",
            parent=styles["Title"],
            fontName=font_name,
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
        )

        # 正文样式
        normal_style = ParagraphStyle(
            "CustomNormal",
            parent=styles["Normal"],
            fontName=font_name,
            fontSize=12,
            spaceAfter=12,
            alignment=TA_LEFT,
        )

        # 表格样式
        table_style = ParagraphStyle(
            "CustomTable",
            parent=styles["Normal"],
            fontName=font_name,
            fontSize=10,
            alignment=TA_LEFT,
        )

        # 构建PDF内容
        story = []

        # 添加标题
        story.append(Paragraph("调解协议", title_style))
        story.append(Spacer(1, 20))

        # 添加案件基本信息
        case_info = f"""
        <b>案件号：</b>{mediation_case.case_number}<br/>
        <b>债务人：</b>{mediation_case.debtor.debtor_name if mediation_case.debtor else '未指定'}<br/>
        <b>债权人：</b>{mediation_case.creditor.creditor_name if mediation_case.creditor else '未指定'}<br/>
        <b>调解员：</b>{mediation_case.mediator.real_name if mediation_case.mediator else '未指定'}<br/>
        """
        story.append(Paragraph(case_info, normal_style))
        story.append(Spacer(1, 20))

        # 添加调解信息
        if mediation_case.confirmed_mediation_config:
            story.append(Paragraph("<b>调解信息：</b>", normal_style))
            mediation_config = mediation_case.confirmed_mediation_config

            # 处理调解配置中的表达式计算
            for key, value in mediation_config.items():
                if isinstance(value, str) and "{" in value and "}" in value:
                    try:
                        # 使用表达式计算器处理包含变量的字符串
                        calculated_value = calculate_expression_with_asset_data(
                            value, mediation_case.asset_package, mediation_case.asset_package_row_number
                        )
                        mediation_info = f"<b>{key}：</b>{calculated_value}<br/>"
                    except Exception as e:
                        logger.warning(f"表达式计算失败: {value}, 错误: {str(e)}")
                        mediation_info = f"<b>{key}：</b>{value}<br/>"
                else:
                    mediation_info = f"<b>{key}：</b>{value}<br/>"

                story.append(Paragraph(mediation_info, normal_style))

            story.append(Spacer(1, 20))

        # 添加调解方案信息
        if mediation_case.confirmed_plan_config:
            story.append(Paragraph("<b>调解方案：</b>", normal_style))
            plan_config = mediation_case.confirmed_plan_config

            for key, value in plan_config.items():
                plan_info = f"<b>{key}：</b>{value}<br/>"
                story.append(Paragraph(plan_info, normal_style))

            story.append(Spacer(1, 20))

        # 添加电子签名和签署日期信息（分两行显示）
        page_width, page_height = A4
        table_width = 200  # 表格宽度

        # 准备签署人信息（第一行）
        if mediation_case.electronic_signature:
            try:
                # 尝试加载电子签名图片
                signature_image = Image(mediation_case.electronic_signature.path, width=100, height=50)
                signature_content = signature_image
            except Exception as e:
                logger.warning(f"无法加载电子签名图片: {str(e)}")
                signature_content = Paragraph("电子签名", table_style)
        else:
            signature_content = Paragraph("签署人：[未签署]", table_style)

        # 准备签署日期信息（第二行）
        if mediation_case.signature_date:
            signature_date_str = mediation_case.signature_date.strftime("%Y年%m月%d日")
            date_content = Paragraph(f"签署日期：{signature_date_str}", table_style)
        else:
            date_content = Paragraph("签署日期：[未签署]", table_style)

        # 创建签名信息表格（两行结构）
        signature_data = [
            [signature_content],  # 第一行：签署人信息
            [date_content],       # 第二行：签署日期
        ]

        signature_table = Table(signature_data, colWidths=[table_width])
        signature_table.setStyle(
            TableStyle(
                [
                    ("ALIGN", (0, 0), (-1, -1), "RIGHT"),  # 右对齐
                    ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 垂直居中
                    ("FONTNAME", (0, 0), (-1, -1), font_name),  # 使用中文字体
                    ("FONTSIZE", (0, 0), (-1, -1), 10),  # 字体大小
                    ("LEFTPADDING", (0, 0), (-1, -1), 5),  # 左边距
                    ("RIGHTPADDING", (0, 0), (-1, -1), 5),  # 右边距
                    ("TOPPADDING", (0, 0), (-1, -1), 3),  # 上边距
                    ("BOTTOMPADDING", (0, 0), (-1, -1), 3),  # 下边距
                    ("ROWBACKGROUNDS", (0, 0), (-1, -1), [colors.white]),  # 背景色
                ]
            )
        )

        # 创建一个包装表格来实现右对齐定位
        wrapper_data = [["", signature_table]]
        wrapper_table = Table(wrapper_data, colWidths=[page_width - table_width, table_width])
        wrapper_table.setStyle(
            TableStyle(
                [
                    ("ALIGN", (1, 0), (1, 0), "RIGHT"),  # 签名表格右对齐
                    ("VALIGN", (0, 0), (-1, -1), "BOTTOM"),  # 底部对齐
                    ("LEFTPADDING", (0, 0), (-1, -1), 0),  # 无边距
                    ("RIGHTPADDING", (0, 0), (-1, -1), 0),  # 无边距
                    ("TOPPADDING", (0, 0), (-1, -1), 0),  # 无边距
                    ("BOTTOMPADDING", (0, 0), (-1, -1), 0),  # 无边距
                ]
            )
        )

        story.append(wrapper_table)

        # 生成PDF
        doc.build(story)

        # 获取PDF内容
        pdf_content = buffer.getvalue()
        buffer.close()

        logger.info(f"PDF内容生成成功，大小: {len(pdf_content)} bytes")
        return pdf_content

    except Exception as e:
        logger.error(f"生成PDF内容时发生异常: {str(e)}")
        return None


def _register_chinese_font():
    """
    注册中文字体，支持跨平台中文字体注册

    字体优先级：微软雅黑 > 黑体 > 宋体 > ReportLab内置中文字体 > Helvetica备选
    支持Windows/macOS/Linux系统的中文字体路径

    Returns:
        str: 注册成功的字体名称
    """
    try:
        # 延迟导入ReportLab相关模块
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont

        # 定义中文字体路径映射（按优先级排序）
        font_paths = [
            # Windows系统字体路径
            ("Microsoft YaHei", "C:/Windows/Fonts/msyh.ttc"),  # 微软雅黑
            ("SimHei", "C:/Windows/Fonts/simhei.ttf"),  # 黑体
            ("SimSun", "C:/Windows/Fonts/simsun.ttc"),  # 宋体

            # macOS系统字体路径
            ("PingFang SC", "/System/Library/Fonts/PingFang.ttc"),  # 苹方
            ("Heiti SC", "/System/Library/Fonts/Helvetica.ttc"),  # 黑体-简

            # Linux系统字体路径
            ("WenQuanYi Micro Hei", "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc"),  # 文泉驿微米黑
            ("Noto Sans CJK SC", "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc"),  # 思源黑体
        ]

        # 尝试注册中文字体
        for font_name, font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont(font_name, font_path))
                    logger.info(f"成功注册中文字体: {font_name}")
                    return font_name
            except Exception as e:
                logger.debug(f"注册字体 {font_name} 失败: {str(e)}")
                continue

        # 如果所有中文字体都注册失败，尝试注册 ReportLab 内置的中文字体
        try:
            from reportlab.pdfbase.cidfonts import UnicodeCIDFont

            pdfmetrics.registerFont(UnicodeCIDFont("STSong-Light"))
            logger.info("使用 ReportLab 内置中文字体: STSong-Light")
            return "STSong-Light"
        except Exception as e:
            logger.warning(f"注册内置中文字体失败: {str(e)}")

        # 最后的备选方案：使用 Helvetica 字体（不支持中文，但不会报错）
        logger.warning("所有中文字体注册失败，使用 Helvetica 字体（可能无法正确显示中文）")
        return "Helvetica"

    except Exception as e:
        logger.error(f"注册中文字体时发生异常: {str(e)}")
        return "Helvetica"


def _cleanup_existing_pdf_files(mediation_case, target_file_name):
    """
    清理调解案件已有的同名PDF文件记录

    在保存新的PDF文件之前，删除该调解案件中已存在的同名PDF文件，
    包括从attachments关系中移除、删除数据库记录和物理文件。

    Args:
        mediation_case: 调解案件对象
        target_file_name: 目标文件名，格式为 "调解协议_{mediation_case_number}.pdf"
    """
    try:
        # 查找该调解案件附件中的同名PDF文件
        existing_files = mediation_case.attachments.filter(file_name=target_file_name)

        if existing_files.exists():
            logger.info(f"发现 {existing_files.count()} 个同名PDF文件，开始清理: {target_file_name}")

            for file_obj in existing_files:
                try:
                    # 1. 从调解案件的附件关系中移除
                    mediation_case.attachments.remove(file_obj)
                    logger.debug(f"已从案件附件中移除文件: {file_obj.file_name}")

                    # 2. 删除物理文件
                    if file_obj.file and os.path.exists(file_obj.file.path):
                        os.remove(file_obj.file.path)
                        logger.debug(f"已删除物理文件: {file_obj.file.path}")

                    # 3. 删除数据库记录
                    file_obj.delete()
                    logger.debug(f"已删除数据库记录: {file_obj.id}")

                except Exception as e:
                    # 单个文件删除失败不影响其他文件的清理
                    logger.warning(f"清理文件 {file_obj.file_name} 时发生错误: {str(e)}")
                    continue

            logger.info(f"同名PDF文件清理完成: {target_file_name}")
        else:
            logger.debug(f"未发现同名PDF文件，无需清理: {target_file_name}")

    except Exception as e:
        # 清理失败不影响新文件的保存
        logger.warning(f"清理同名PDF文件时发生异常: {str(e)}")


@conditional_task_decorator
def batch_update_mediation_case_status_async(case_ids: List[int]) -> Dict:
    """
    异步批量更新调解案件状态

    该任务在后台异步执行，适用于大批量案件状态更新操作。主要功能：
    1. 批量将案件状态从"待发起"更新为"已发起"
    2. 设置案件的发起时间
    3. 返回详细的更新结果

    Args:
        case_ids (List[int]): 需要更新状态的案件ID列表

    Returns:
        Dict: Celery任务执行结果，包含更新数量和案件详情
    """
    logger.info(f"开始异步批量更新调解案件状态，案件数量: {len(case_ids)}")

    try:
        # 在函数内部导入，确保Django环境已初始化
        from django.db import transaction
        from django.utils import timezone
        from apps.mediation_management.models import MediationCase
        from ops_management.celery import CeleryResult

        with transaction.atomic():
            # 批量更新案件状态和发起时间
            updated_count = MediationCase.objects.filter(
                id__in=case_ids,
                case_status='draft'  # 确保只更新待发起状态的案件
            ).update(
                case_status='initiated',
                initiate_date=timezone.now()
            )
            
            # 获取更新后的案件信息用于返回
            updated_cases = MediationCase.objects.filter(
                id__in=case_ids
            ).values('id', 'case_number', 'case_status', 'initiate_date')
            
            result = {
                'updated_count': updated_count,
                'updated_cases': list(updated_cases),
                'total_requested': len(case_ids)
            }

        logger.info(f"异步批量更新调解案件状态完成，更新数量: {updated_count}/{len(case_ids)}")

        # 返回成功结果
        return CeleryResult.success("batch_update_mediation_case_status_async")

    except Exception as e:
        logger.error(f"异步批量更新调解案件状态失败: {str(e)}")
        # 返回失败结果
        return CeleryResult.fail("batch_update_mediation_case_status_async", str(e))


if __name__ == "__main__":
    """
    调试和测试异步任务功能
    可以直接运行此文件来测试异步任务，无需通过Celery任务队列
    """
    import os
    import sys
    import django

    # 设置Django环境
    def setup_django_environment():
        """设置Django环境，用于调试和测试"""
        # 添加项目根目录到Python路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        sys.path.insert(0, project_root)
        
        # 设置Django设置模块
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
        
        # 初始化Django
        django.setup()

    print("=" * 60)
    print("调解管理异步任务调试测试")
    print("=" * 60)

    # 检查Celery是否可用
    if CELERY_AVAILABLE:
        print("✅ Celery环境可用")

        # 测试异步任务
        test_case_ids_input = input("请输入测试用的案件ID列表（逗号分隔）: ").strip()

        if test_case_ids_input:
            try:
                # 解析案件ID列表
                test_case_ids = [int(id.strip()) for id in test_case_ids_input.split(',') if id.strip()]
                
                if test_case_ids:
                    print(f"\n正在测试异步任务...")
                    print(f"案件ID列表: {test_case_ids}")

                    try:
                        # 初始化Django环境
                        setup_django_environment()
                        
                        # 直接调用函数（同步方式，用于测试）
                        result = batch_update_mediation_case_status_async(test_case_ids)
                        print(f"✅ 任务执行成功: {result}")
                    except Exception as e:
                        print(f"❌ 任务执行失败: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print("❌ 案件ID列表为空，跳过测试")
            except ValueError as e:
                print(f"❌ 案件ID格式错误: {e}")
        else:
            print("❌ 测试参数不完整，跳过测试")
    else:
        print("❌ Celery环境不可用，请检查Django环境和Celery配置")

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
