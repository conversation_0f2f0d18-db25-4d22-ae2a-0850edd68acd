#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : urls.py
<AUTHOR> JT_DA
@Date     : 2025/07/24
@File_Desc: 微信应用URL配置
"""

from django.urls import path
from apps.wechat.views import faceid_views, wechat_auth_views, sms_verification_view

urlpatterns = [
    # 腾讯云FaceID人脸核身认证接口
    path("faceid/auth/", faceid_views.FaceIDAuthView.as_view(), name="faceid_auth"),
    # 微信登录认证接口
    path("login/", wechat_auth_views.WechatLoginView.as_view(), name="wechat_login"),
    # 微信刷新令牌接口
    path("refresh/", wechat_auth_views.WechatRefreshView.as_view(), name="wechat_refresh"),
    # 短信验证码发送接口
    path("sms/<str:case_number>/send/", sms_verification_view.SMSVerificationView.as_view(), name="sms_verification"),
]
