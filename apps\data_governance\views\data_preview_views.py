#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : data_preview_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/15
@File_Desc: 数据预览视图 - 提供资产包数据的预览功能
"""

import os
import pandas as pd
from django.http import Http404
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.data_governance.models import AssetPackageManagement
from utils.ajax_result import AjaxResult
from utils.date_helper import format_dataframe_dates


def _mask_data(value, prefix_keep_chars, suffix_keep_chars):
    """
    数据脱敏处理函数
    
    Args:
        value: 原始数据值
        prefix_keep_chars: 前保留字符数
        suffix_keep_chars: 后保留字符数
    
    Returns:
        str: 脱敏后的数据
    """
    if pd.isna(value) or value == '':
        return value
    
    value_str = str(value)
    value_len = len(value_str)
    
    # 如果字符串长度小于等于保留字符数总和，则不进行脱敏
    if value_len <= (prefix_keep_chars + suffix_keep_chars):
        return value_str
    
    # 计算需要脱敏的字符数
    mask_chars_count = value_len - prefix_keep_chars - suffix_keep_chars
    
    # 构建脱敏后的字符串
    prefix = value_str[:prefix_keep_chars] if prefix_keep_chars > 0 else ''
    suffix = value_str[-suffix_keep_chars:] if suffix_keep_chars > 0 else ''
    masked_part = '*' * mask_chars_count
    
    return prefix + masked_part + suffix


def _generate_column_config(column_name, field_type='text'):
    """
    生成列配置信息
    
    Args:
        column_name: 列名
        field_type: 字段类型
    
    Returns:
        dict: 列配置字典
    """
    # 根据字段类型设置列属性
    type_config = {
        'text': {'type': 'text', 'width': 150, 'align': 'center'},
        'date': {'type': 'date', 'width': 120, 'align': 'center'},
        'numeric': {'type': 'number', 'width': 100, 'align': 'center'},
        'amount': {'type': 'currency', 'width': 120, 'align': 'center'},
    }
    
    config = type_config.get(field_type, type_config['text'])
    
    return {
        'key': column_name,
        'label': column_name,
        'type': config['type'],
        'width': config['width'],
        'align': config['align']
    }


class PreviewMappedDataView(APIView):
    """
    映射数据预览视图

    提供资产包数据的预览功能，支持字段映射转换和数据脱敏处理。
    该视图会根据已配置的字段映射关系，将Excel原始字段名转换为标准字段名，并对标记为敏感的字段进行脱敏处理。
    """

    def get(self, request, package_id):
        """
        获取资产包的映射数据预览

        根据资产包ID获取经过字段映射和数据脱敏处理的数据预览。
        该接口会读取资产包关联的Excel文件，应用已配置的字段映射关系，对敏感字段进行脱敏处理。

        **请求参数：**
        - package_id (整数, 必需): 资产包的唯一标识ID，通过URL路径传递

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "package_name": "2025年第一批资产包",
                "file_name": "客户数据.xlsx",
                "columns": [
                    {
                        "key": "客户姓名",
                        "label": "客户姓名",
                        "type": "text",
                        "width": 150,
                        "align": "left"
                    },
                    {
                        "key": "联系电话",
                        "label": "联系电话",
                        "type": "text",
                        "width": 150,
                        "align": "left"
                    }
                ],
                "data": [
                    {
                        "客户姓名": "张*三",
                        "联系电话": "138****5678"
                    },
                    {
                        "客户姓名": "李*四",
                        "联系电话": "139****1234"
                    }
                ],
                "total": 100
            }
        }
        ```
        """
        try:
            # 获取资产包实例
            asset_package = AssetPackageManagement.objects.get(id=package_id)
        except AssetPackageManagement.DoesNotExist:
            raise Http404("资产包不存在")

        # 检查是否有原文件
        if not asset_package.source_file:
            return AjaxResult.fail(msg="资产包缺少原文件")

        # 检查文件是否存在
        if not os.path.exists(asset_package.source_file.path):
            return AjaxResult.fail(msg="原文件不存在")

        try:
            # 使用pandas读取Excel文件
            df = pd.read_excel(asset_package.source_file.path)

            # 对DataFrame中的日期列进行标准化格式处理
            df = format_dataframe_dates(df)

            # 获取字段映射关系
            field_mappings = asset_package.field_mappings.select_related('mapped_field_config').all()

            # 创建映射字典：原字段名 -> 映射配置
            mapping_dict = {}
            for mapping in field_mappings:
                if mapping.mapped_field_config:  # 只处理有映射配置的字段
                    mapping_dict[mapping.original_field_name] = mapping.mapped_field_config

            # 处理数据：字段映射和脱敏
            processed_data = []
            columns = []

            # 确定要显示的列（只显示有映射配置的列）
            display_columns = []
            for original_col in df.columns:
                if original_col in mapping_dict:
                    field_config = mapping_dict[original_col]
                    display_columns.append({
                        'original_name': original_col,
                        'mapped_name': field_config.field_name,
                        'config': field_config
                    })

            # 生成列配置
            for col_info in display_columns:
                field_config = col_info['config']
                column_config = _generate_column_config(
                    col_info['mapped_name'],
                    field_config.field_type
                )
                columns.append(column_config)

            # 处理数据行
            for index, row in df.iterrows():
                processed_row = {}

                for col_info in display_columns:
                    original_name = col_info['original_name']
                    mapped_name = col_info['mapped_name']
                    field_config = col_info['config']

                    # 获取原始值
                    original_value = row.get(original_name, '')

                    # 如果需要脱敏处理
                    if field_config.is_masked:
                        processed_value = _mask_data(
                            original_value,
                            field_config.prefix_keep_chars,
                            field_config.suffix_keep_chars
                        )
                    else:
                        processed_value = original_value

                    # 处理NaN值
                    if pd.isna(processed_value):
                        processed_value = ''

                    processed_row[mapped_name] = processed_value

                processed_data.append(processed_row)

            # 构建返回数据
            response_data = {
                'package_name': asset_package.package_name,
                'file_name': os.path.basename(asset_package.source_file.name),
                'columns': columns,
                'data': processed_data,
                'total': len(processed_data)
            }

            return AjaxResult.success(data=response_data)

        except Exception as e:
            return AjaxResult.fail(msg=f"数据处理失败: {str(e)}")


class PreviewRawDataView(APIView):
    """
    原始数据预览视图

    提供资产包原始Excel文件的数据预览功能，不进行任何字段映射或数据脱敏处理。
    该视图直接读取Excel文件内容，保持原始字段名和数据值，主要用于用户查看原始数据结构和内容。
    """

    def get(self, request, package_id):
        """
        获取资产包的原始数据预览

        根据资产包ID获取Excel文件的原始数据预览，不进行任何处理。
        该接口直接读取Excel文件内容，保持原始字段名和数据值，为用户提供完整的原始数据视图。

        **请求参数：**
        - package_id (整数, 必需): 资产包的唯一标识ID，通过URL路径传递

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "package_name": "2025年第一批资产包",
                "file_name": "客户数据.xlsx",
                "columns": [
                    {
                        "key": "客户姓名",
                        "label": "客户姓名",
                        "type": "text",
                        "width": 150,
                        "align": "left"
                    },
                    {
                        "key": "联系电话",
                        "label": "联系电话",
                        "type": "text",
                        "width": 150,
                        "align": "left"
                    }
                ],
                "data": [
                    {
                        "客户姓名": "张三",
                        "联系电话": "13812345678"
                    },
                    {
                        "客户姓名": "李四",
                        "联系电话": "13987654321"
                    }
                ],
                "total": 100
            }
        }
        ```
        """
        try:
            # 获取资产包实例
            asset_package = AssetPackageManagement.objects.get(id=package_id)
        except AssetPackageManagement.DoesNotExist:
            raise Http404("资产包不存在")

        # 检查是否有原文件
        if not asset_package.source_file:
            return AjaxResult.fail(msg="资产包缺少原文件")

        # 检查文件是否存在
        if not os.path.exists(asset_package.source_file.path):
            return AjaxResult.fail(msg="原文件不存在")

        try:
            # 使用pandas读取Excel文件
            df = pd.read_excel(asset_package.source_file.path)

            # 对DataFrame中的日期列进行标准化格式处理
            df = format_dataframe_dates(df)

            # 生成列配置（使用原始列名）
            columns = []
            for column_name in df.columns:
                column_config = _generate_column_config(column_name)
                columns.append(column_config)

            # 处理数据行
            raw_data = []
            for index, row in df.iterrows():
                row_data = {}
                for column_name in df.columns:
                    value = row.get(column_name, '')
                    # 处理NaN值
                    if pd.isna(value):
                        value = ''
                    row_data[column_name] = value
                raw_data.append(row_data)

            # 构建返回数据
            response_data = {
                'package_name': asset_package.package_name,
                'file_name': asset_package.original_file_name,
                'columns': columns,
                'data': raw_data,
                'total': len(raw_data)
            }

            return AjaxResult.success(data=response_data)

        except Exception as e:
            return AjaxResult.fail(msg=f"数据处理失败: {str(e)}")
