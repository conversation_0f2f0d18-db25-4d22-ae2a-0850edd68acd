#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : tasks.py
<AUTHOR> JT_DA
@Date     : 2025/07/24
@File_Desc: 微信相关异步任务，处理人脸核身结果等后台任务
"""

import logging

# 延迟导入Django相关模块，避免在文件加载时就需要Django环境
try:
    # 尝试导入Celery相关模块（在Django环境中）
    from ops_management.celery import app, CeleryResult

    CELERY_AVAILABLE = True
except ImportError:
    # 如果无法导入，说明Django环境未初始化，设置标志位
    CELERY_AVAILABLE = False
    app = None
    CeleryResult = None

# 获取日志记录器
logger = logging.getLogger(__name__)


# 创建装饰器函数，支持条件装饰
def conditional_task_decorator(func):
    """条件任务装饰器，只在Celery可用时应用@app.task装饰器"""
    if CELERY_AVAILABLE and app:
        return app.task()(func)
    return func


@conditional_task_decorator
def process_face_auth_result_async(auth_token: str, biz_token: str):
    """
    异步处理腾讯云人脸核身结果

    该任务在后台异步执行，不阻塞主流程。主要功能：
    1. 调用腾讯云API获取核身结果
    2. 判断核身是否成功
    3. 提取用户信息并提交到认证服务器

    Args:
        auth_token (str): 认证令牌，用作请求头
        biz_token (str): 人脸核身业务标识

    Returns:
        dict: Celery任务执行结果，包含任务状态和错误信息（如有）
    """
    logger.info(f"开始异步处理人脸核身结果，biz_token: {biz_token}")

    try:
        # 在函数内部导入，确保Django环境已初始化
        from utils.tencent_faceid_utils import process_face_auth_result
        from apps.user.tasks import sync_auth_user_local
        from ops_management.celery import CeleryResult

        # 调用核身结果处理函数
        result = process_face_auth_result(auth_token, biz_token)

        logger.info(f"异步处理人脸核身结果完成，biz_token: {biz_token}, 结果: {result}")

        # 核身结果处理完成后，同步认证服务器用户数据到本地
        logger.info("开始同步认证服务器用户数据到本地数据库")
        sync_result = sync_auth_user_local()
        logger.info(f"用户数据同步完成，结果: {sync_result}")

        # 返回成功结果
        return CeleryResult.success("process_face_auth_result_async")

    except Exception as e:
        # 记录异常日志
        logger.error(f"异步处理人脸核身结果时发生异常，biz_token: {biz_token}, 错误: {str(e)}")

        # 返回失败结果
        return CeleryResult.fail("process_face_auth_result_async", str(e))


def setup_django_environment():
    """设置Django环境，用于独立运行时初始化"""
    import os
    import sys
    import django

    # 获取项目根目录路径（从 apps/wechat/tasks.py 向上两级到项目根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))  # apps/wechat/
    project_root = os.path.dirname(os.path.dirname(current_dir))  # 项目根目录

    # 添加项目根目录到Python路径
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

    # 设置Django设置模块
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ops_management.settings")

    # 初始化Django
    django.setup()

    print("Django环境初始化完成")


if __name__ == "__main__":
    """
    调试和测试代码块

    使用说明：
    1. Django环境将自动初始化
    2. 确保Celery服务正在运行
    3. 运行此文件进行测试：
       python apps/wechat/tasks.py
    """

    try:
        print("正在初始化Django环境...")
        setup_django_environment()

        print("=" * 60)
        print("微信异步任务调试测试")
        print("=" * 60)

        # 测试异步任务
        test_auth_token = input("请输入测试用的认证令牌: ").strip()
        test_biz_token = input("请输入测试用的业务Token: ").strip()

        if test_auth_token and test_biz_token:
            print(f"\n正在测试异步任务...")
            print(
                f"认证令牌: {test_auth_token[:20]}..." if len(test_auth_token) > 20 else f"认证令牌: {test_auth_token}"
            )
            print(f"业务Token: {test_biz_token}")

            print("开始执行人脸核身结果处理...")
            result = process_face_auth_result_async(test_auth_token, test_biz_token)
            print(f"人脸核身结果处理完成，结果：{result}")
        else:
            print("❌ 测试参数不完整，跳过测试")

        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)

    except Exception as e:
        print(f"执行过程中发生错误：{e}")
        import traceback

        traceback.print_exc()
