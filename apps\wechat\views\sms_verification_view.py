#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : sms_verification_view.py
<AUTHOR> JT_DA
@Date     : 2025/08/05
@File_Desc: 短信验证码发送视图 - 微信小程序专用
"""

import logging
import random
from rest_framework.generics import GenericAPIView
from rest_framework import status
from django.shortcuts import get_object_or_404

from apps.mediation_management.models import MediationCase
from apps.wechat.serializers.sms_verification_serializers import SMSVerificationSerializer
from utils.permission_helper import WechatBasicPermission
from utils.ajax_result import AjaxResult
# from utils.tencent_sms_utils import send_sms  # 暂时注释掉实际发送代码

# 获取日志记录器
logger = logging.getLogger(__name__)


class SMSVerificationView(GenericAPIView):
    """
    短信验证码发送视图（微信小程序专用）

    **概要说明**
    微信小程序端专用接口，用于向指定手机号码发送6位数字验证码。
    该接口通过WechatBasicPermission权限验证微信用户身份，
    验证码将保存到session中，用于后续的手机号码验证操作。

    **功能特性**
    - 生成6位纯数字随机验证码
    - 验证码存储到session中，key格式为：sms_code_{case_number}_{phone_number}
    - 集成腾讯云短信发送服务（当前版本暂时注释）
    - 完整的错误处理和日志记录
    """

    # 使用微信基础权限认证
    permission_classes = [WechatBasicPermission]
    
    serializer_class = SMSVerificationSerializer

    def post(self, request, case_number=None):
        """
        发送短信验证码

        **概要说明**
        向指定的手机号码发送6位数字验证码，用于验证手机号码的所有权。
        验证码将保存到用户session中，有效期为session生命周期。

        **请求参数**
        - case_number (字符串, 必需): 调解案件号，通过URL路径传递

        **请求体参数**
        - phone_number (字符串, 必需): 接收验证码的手机号码，必须为11位数字

        **请求数据示例**
        ```
        POST /wechat/sms/GZTJ20250805ABC123/send/
        Content-Type: application/json

        {
            "phone_number": "13812345678"
        }
        ```

        **成功响应示例**
        ```json
        {
            "code": 200,
            "msg": "验证码发送成功，请注意查收短信",
            "state": "success",
            "data": {
                "case_number": "GZTJ20250805ABC123",
                "phone_number": "138****5678",
                "expires_in": 300
            }
        }
        ```

        **错误响应示例**
        ```json
        {
            "code": 400,
            "msg": "手机号码格式不正确，请输入有效的11位手机号码",
            "state": "fail",
            "data": null
        }
        ```

        **案件不存在响应示例**
        ```json
        {
            "code": 404,
            "msg": "调解案件不存在",
            "state": "fail",
            "data": null
        }
        ```
        """
        try:
            # 验证调解案件是否存在
            mediation_case = get_object_or_404(MediationCase, case_number=case_number)
            logger.info(f"开始处理短信验证码发送请求，案件号: {case_number}")

            # 验证请求参数
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            phone_number = serializer.validated_data['phone_number']
            
            # 生成6位纯数字随机验证码
            sms_code = str(random.randint(100000, 999999))
            
            # 构建session存储key
            session_key = f"sms_code_{case_number}_{phone_number}"
            
            # 将验证码保存到session中
            request.session[session_key] = sms_code
            request.session.set_expiry(300)  # 设置5分钟过期时间
            
            logger.info(f"验证码已生成并保存到session，案件号: {case_number}, 手机号: {phone_number[:3]}****{phone_number[-4:]}")
            
            # TODO: 集成腾讯云短信发送服务
            # 当前版本暂时注释掉实际发送代码，用于开发测试
            """
            try:
                # 调用腾讯云短信发送服务
                sms_response = send_sms(
                    phone_numbers=[phone_number],
                    template_id="验证码模板ID",  # 需要配置实际的模板ID
                    template_params=[sms_code],
                    sms_type="verification",
                    mediation_case_id=mediation_case.id
                )
                
                logger.info(f"短信发送成功，案件号: {case_number}, 手机号: {phone_number[:3]}****{phone_number[-4:]}")
                
            except Exception as sms_error:
                logger.error(f"短信发送失败，案件号: {case_number}, 错误: {str(sms_error)}")
                # 短信发送失败时清除session中的验证码
                if session_key in request.session:
                    del request.session[session_key]
                return AjaxResult.fail(msg="短信发送失败，请稍后重试")
            """
            
            # 返回成功响应（开发阶段）
            return AjaxResult.success(
                msg="验证码发送成功，请注意查收短信",
                data={
                    "case_number": case_number,
                    "phone_number": f"{phone_number[:3]}****{phone_number[-4:]}",  # 脱敏显示
                    "expires_in": 300  # 过期时间（秒）
                }
            )
            
        except MediationCase.DoesNotExist:
            logger.warning(f"调解案件不存在，案件号: {case_number}")
            return AjaxResult.fail(msg="调解案件不存在", code=404)
            
        except Exception as e:
            logger.error(f"短信验证码发送过程中发生异常，案件号: {case_number}, 错误: {str(e)}")
            return AjaxResult.server_error(msg="服务器内部错误，请联系管理员")
