# -*- coding: utf-8 -*-
"""
对外通信模型定义
包含语音外呼和短信发送相关的数据模型
"""
from django.db import models
from ops_management.base_model import BaseModel
from apps.counterparty.models import CreditorBasicInfo, DebtorBasicInfo


class VoiceCallRecord(BaseModel):
    """语音外呼记录模型"""

    # 呼叫状态选择项
    CALL_STATUS_CHOICES = [
        ("not_connected", "未接通"),
        ("connected", "已接通"),
        ("busy", "忙线"),
        ("power_off", "关机"),
        ("no_answer", "无人接听"),
        ("invalid_number", "无效号码"),
        ("rejected", "拒接"),
        ("failed", "呼叫失败"),
    ]

    # 被叫号码 - 支持手机号和固话
    called_number = models.CharField(max_length=20, verbose_name="被叫号码", help_text="被叫方的手机号码或固定电话号码")

    # 主叫号码 - 外呼系统使用的号码
    caller_number = models.CharField(max_length=20, verbose_name="主叫号码", help_text="外呼系统使用的主叫号码")

    # 呼叫状态 - 记录本次外呼的结果状态
    call_status = models.CharField(
        max_length=20,
        choices=CALL_STATUS_CHOICES,
        default="not_connected",
        verbose_name="呼叫状态",
        help_text="本次外呼的结果状态",
    )

    # 通话时长 - 以秒为单位，仅在接通时有效
    call_duration = models.PositiveIntegerField(
        default=0, verbose_name="通话时长(秒)", help_text="实际通话时长，单位为秒，未接通时为0"
    )

    # 呼叫开始时间 - 发起呼叫的时间戳
    call_start_time = models.DateTimeField(verbose_name="呼叫开始时间", help_text="发起外呼的具体时间")

    # 呼叫结束时间 - 呼叫结束的时间戳，可能为空
    call_end_time = models.DateTimeField(
        null=True, blank=True, verbose_name="呼叫结束时间", help_text="呼叫结束的具体时间，未接通时可能为空"
    )

    # 录音文件路径 - 存储通话录音文件的相对路径
    recording_file_path = models.CharField(
        max_length=500,
        null=True,
        blank=True,
        verbose_name="录音文件路径",
        help_text="通话录音文件的存储路径，未接通时为空",
    )

    # 外呼任务ID/批次号 - 用于标识外呼任务或批次
    task_batch_id = models.CharField(
        max_length=100, null=True, blank=True, verbose_name="外呼任务批次号", help_text="外呼任务或批次的唯一标识符"
    )

    # 债权人关联 - 关联债权人信息，表示代表哪个债权人进行外呼
    creditor = models.ForeignKey(
        CreditorBasicInfo,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="关联债权人",
        help_text="代表进行外呼的债权人信息",
    )

    # 债务人关联 - 关联债务人信息，表示外呼的目标客户
    debtor = models.ForeignKey(
        DebtorBasicInfo,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="关联债务人",
        help_text="外呼目标的债务人信息",
    )

    # 外呼结果备注 - 记录外呼过程中的重要信息和结果
    call_result_notes = models.TextField(
        null=True, blank=True, verbose_name="外呼结果备注", help_text="记录外呼过程中的重要信息、客户反馈或处理结果"
    )

    class Meta:
        db_table = "t_voice_call_record"
        verbose_name = "语音外呼记录"
        verbose_name_plural = "语音外呼记录"
        ordering = ["-call_start_time"]  # 按呼叫开始时间倒序排列

    def __str__(self):
        return f"{self.caller_number} -> {self.called_number} ({self.get_call_status_display()})"


class SmsRecord(BaseModel):
    """短信发送记录模型"""

    # 发送状态选择项
    SMS_STATUS_CHOICES = [
        ("sent_success", "发送成功"),
        ("sent_failed", "发送失败"),
    ]

    # 短信类型选择项
    SMS_TYPE_CHOICES = [
        ("notification", "通知短信"),
        ("verification", "验证码短信"),
        ("reminder", "提醒短信"),
        ("other", "其他"),
    ]

    # 接收手机号 - 短信接收方的手机号码
    recipient_phone = models.CharField(max_length=20, verbose_name="接收手机号", help_text="短信接收方的手机号码")

    # 短信内容 - 实际发送的短信文本内容
    sms_content = models.TextField(verbose_name="短信内容", help_text="实际发送的短信文本内容")

    # 发送状态 - 记录短信发送的当前状态
    sms_status = models.CharField(
        max_length=20,
        choices=SMS_STATUS_CHOICES,
        default="sent_success",
        verbose_name="发送状态",
        help_text="短信发送的当前状态",
    )

    # 发送时间 - 短信实际发送的时间戳
    send_time = models.DateTimeField(null=True, blank=True, verbose_name="发送时间", help_text="短信实际发送的时间戳")

    # 送达时间 - 短信成功送达的时间戳
    delivery_time = models.DateTimeField(
        null=True, blank=True, verbose_name="送达时间", help_text="短信成功送达到接收方的时间戳"
    )

    # 任务批次号 - 对应腾讯云API响应的RequestId
    task_batch_id = models.CharField(
        max_length=100, null=True, blank=True, verbose_name="任务批次号", help_text="对应腾讯云API响应的RequestId"
    )

    # 调解案件关联 - 关联调解案件信息
    mediation_case = models.ForeignKey(
        "mediation_management.MediationCase",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="关联调解案件",
        help_text="短信关联的调解案件信息",
    )

    # 失败原因 - 当发送失败时记录具体的失败原因
    failure_reason = models.TextField(
        null=True, blank=True, verbose_name="失败原因", help_text="短信发送或送达失败时的具体原因描述"
    )

    # 短信类型 - 标识短信的业务类型
    sms_type = models.CharField(
        max_length=20,
        choices=SMS_TYPE_CHOICES,
        default="notification",
        verbose_name="短信类型",
        help_text="短信的业务类型分类",
    )

    class Meta:
        db_table = "t_sms_record"
        verbose_name = "短信发送记录"
        verbose_name_plural = "短信发送记录"

    def __str__(self):
        return f"{self.recipient_phone} - {self.get_sms_type_display()} ({self.get_sms_status_display()})"
