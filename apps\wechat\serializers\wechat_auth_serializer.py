#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : wechat_auth_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/07/23
@File_Desc: 微信认证相关序列化器，用于验证微信登录和刷新令牌请求参数
"""

from rest_framework import serializers


class WechatLoginSerializer(serializers.Serializer):
    """微信登录序列化器

    用于验证和处理微信小程序登录请求的输入数据。
    验证微信授权码、用户昵称和头像URL的格式和有效性。
    """

    js_code = serializers.CharField(
        required=True,
        max_length=128,
        help_text="微信小程序授权码，用于获取用户openid和session_key",
        error_messages={
            'required': '微信授权码不能为空',
            'max_length': '微信授权码长度不能超过128位'
        }
    )

    nickname = serializers.CharField(
        required=True,
        max_length=64,
        help_text="用户昵称，微信用户的显示名称",
        error_messages={
            'required': '用户昵称不能为空',
            'max_length': '用户昵称长度不能超过64位'
        }
    )

    avatar_url = serializers.URLField(
        required=True,
        max_length=512,
        help_text="用户头像URL，格式如 http://example.com/avatar.jpg",
        error_messages={
            'required': '用户头像URL不能为空',
            'invalid': '用户头像URL格式不正确',
            'max_length': '用户头像URL长度不能超过512位'
        }
    )

    def validate_js_code(self, value):
        """验证微信授权码格式

        检查微信授权码是否符合基本格式要求：
        - 不能包含空格
        - 不能为空字符串

        Args:
            value (str): 微信授权码

        Returns:
            str: 验证通过的微信授权码

        Raises:
            ValidationError: 当授权码格式不正确时抛出
        """
        if not value or not value.strip():
            raise serializers.ValidationError("微信授权码不能为空")

        if ' ' in value:
            raise serializers.ValidationError("微信授权码不能包含空格")

        return value.strip()

    def validate_nickname(self, value):
        """验证用户昵称格式

        检查用户昵称是否符合基本要求：
        - 不能为空字符串
        - 去除首尾空格

        Args:
            value (str): 用户昵称

        Returns:
            str: 验证通过的用户昵称

        Raises:
            ValidationError: 当昵称格式不正确时抛出
        """
        if not value or not value.strip():
            raise serializers.ValidationError("用户昵称不能为空")

        return value.strip()


class WechatRefreshSerializer(serializers.Serializer):
    """微信刷新令牌序列化器

    用于微信session_key刷新请求的序列化器。
    此接口无需额外参数，仅用于统一API格式和文档生成。
    """

    # 无需额外字段，仅通过Authorization头识别用户身份
    pass
