# -*- coding: utf-8 -*-
"""
短信发送记录视图集
"""
from rest_framework import viewsets
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend

from apps.outbound_communication.models import SmsRecord
from apps.outbound_communication.serializers import SmsRecordListSerializer
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult


class BaseSmsRecordViewSet(viewsets.ReadOnlyModelViewSet):
    """
    短信发送记录基础ViewSet

    提供短信发送记录的基础只读操作功能，包括列表查询、详情查看、过滤和搜索。
    所有短信记录按创建时间倒序排列，支持分页展示。
    """

    queryset = SmsRecord.objects.all().order_by("-created_time")
    serializer_class = SmsRecordListSerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["sms_status", "sms_type", "mediation_case", "task_batch_id"]  # 支持按状态、类型、调解案件、任务批次号过滤
    search_fields = ["recipient_phone", "sms_content", "failure_reason"]  # 支持按手机号、内容、失败原因搜索


class SmsRecordViewSet(BaseSmsRecordViewSet):
    """
    短信发送记录管理ViewSet

    提供短信发送记录的完整管理功能，包括分页列表查询和详情查看。
    支持按发送状态、短信类型、调解案件等多维度过滤和模糊搜索。
    """

    def list(self, request, *args, **kwargs):
        """
        获取短信发送记录列表

        提供短信发送记录的分页查询功能，支持按发送状态、短信类型、关联对象等多维度过滤，
        以及按手机号、内容、失败原因进行模糊搜索。记录按创建时间倒序排列。

        **请求参数**
        查询参数：
        - sms_status (string, 可选): 发送状态过滤，可选值：pending(待发送)、sending(发送中)、sent_success(发送成功)、sent_failed(发送失败)
        - sms_type (string, 可选): 短信类型过滤，可选值：notification(通知短信)、verification(验证码短信)、reminder(提醒短信)、collection(催收短信)、other(其他)
        - creditor (integer, 可选): 债权人ID，过滤指定债权人相关的短信记录
        - debtor (integer, 可选): 债务人ID，过滤指定债务人相关的短信记录
        - task_batch_id (string, 可选): 任务批次号，过滤指定批次的短信记录
        - search (string, 可选): 搜索关键词，支持按接收手机号、短信内容、失败原因进行模糊搜索
        - page (integer, 可选): 页码，默认为1
        - page_size (integer, 可选): 每页记录数，默认为20，最大100

        **请求数据示例**
        该接口为GET请求，无需请求体。

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "success",
            "data": {
                "count": 320,
                "next": "http://example.com/api/sms_records/?page=2",
                "previous": null,
                "results": [
                    {
                        "id": 1,
                        "recipient_phone": "13812345678",
                        "sms_content": "尊敬的客户，您的账单已生成，请及时查看并处理。如有疑问请联系客服。",
                        "content_preview": "尊敬的客户，您的账单已生成，请及时查看并处理。如有疑问请联系客服。",
                        "content_length": 35,
                        "sms_status": "sent_success",
                        "sms_status_cn": "发送成功",
                        "sms_type": "notification",
                        "sms_type_cn": "通知短信",
                        "send_time": "2025-07-18 10:30:00",
                        "delivery_time": "2025-07-18 10:30:05",
                        "task_batch_id": "BATCH_20250718_001",
                        "creditor": 1,
                        "creditor_name": "某银行股份有限公司",
                        "debtor": 1,
                        "debtor_name": "张三",
                        "failure_reason": null
                    }
                ]
            }
        }
        ```
        """
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        """
        获取短信发送记录详情

        根据短信记录ID获取完整的详细信息，包括基本信息、发送状态、关联对象和时间记录。
        提供短信记录的全面视图，用于详情页面展示、问题排查和数据分析。

        **请求参数**
        路径参数：
        - id (integer, 必需): 短信记录的唯一标识符

        **请求数据示例**
        该接口为GET请求，无需请求体。

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "success",
            "data": {
                "id": 1,
                "recipient_phone": "13812345678",
                "sms_content": "尊敬的客户，您的账单已生成，请及时查看并处理。如有疑问请联系客服热线************。",
                "content_preview": "尊敬的客户，您的账单已生成，请及时查看并处理。如有疑问请联系客服热线************。",
                "content_length": 42,
                "sms_status": "sent_success",
                "sms_status_cn": "发送成功",
                "sms_type": "notification",
                "sms_type_cn": "通知短信",
                "send_time": "2025-07-18 10:30:00",
                "delivery_time": "2025-07-18 10:30:05",
                "task_batch_id": "a0aabda6-cf91-4f3e-a81f-9198114a2279",
                "mediation_case": 1,
                "mediation_case_number": "GZTJ20250718ABC123",
                "failure_reason": null
            }
        }
        ```
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)
