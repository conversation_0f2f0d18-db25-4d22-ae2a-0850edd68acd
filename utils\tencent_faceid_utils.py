#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : tencent_faceid_utils.py
<AUTHOR> JT_DA
@Date     : 2025/07/24
@File_Desc: 腾讯云FaceID API工具函数
"""

import os
import hashlib
import hmac
import json
import time
import requests
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any

from ops_management.settings import AUTH_SERVER_URL

# 获取日志记录器
logger = logging.getLogger(__name__)


def _get_config_value(key: str, default: Any = None) -> Any:
    """
    从gunicorn_config.py配置文件中获取配置值

    Args:
        key: 配置键名
        default: 默认值

    Returns:
        配置值或默认值
    """
    try:
        import gunicorn_config

        return gunicorn_config.env.get(key, default)
    except (ImportError, AttributeError):
        return default


def _sign(key: bytes, msg: str) -> bytes:
    """
    使用HMAC-SHA256算法对消息进行签名

    Args:
        key: 签名密钥
        msg: 待签名的消息

    Returns:
        签名结果
    """
    return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()


def _build_tencent_headers(
    action: str,
    payload: str,
    secret_id: str,
    secret_key: str,
    service: str = "faceid",
    version: str = "2018-03-01",
    region: str = "",
    token: str = "",
) -> Dict[str, str]:
    """
    构建腾讯云API请求头

    Args:
        action: API动作名称
        payload: 请求体JSON字符串
        secret_id: 腾讯云SecretId
        secret_key: 腾讯云SecretKey
        service: 服务名称，默认为faceid
        version: API版本，默认为2018-03-01
        region: 地域，默认为空
        token: 临时凭证token，默认为空

    Returns:
        包含认证信息的请求头字典
    """
    host = f"{service}.tencentcloudapi.com"
    algorithm = "TC3-HMAC-SHA256"
    timestamp = int(time.time())
    date = datetime.fromtimestamp(timestamp, timezone.utc).strftime("%Y-%m-%d")

    # ************* 步骤 1：拼接规范请求串 *************
    http_request_method = "POST"
    canonical_uri = "/"
    canonical_querystring = ""
    ct = "application/json; charset=utf-8"
    canonical_headers = f"content-type:{ct}\nhost:{host}\nx-tc-action:{action.lower()}\n"
    signed_headers = "content-type;host;x-tc-action"
    hashed_request_payload = hashlib.sha256(payload.encode("utf-8")).hexdigest()
    canonical_request = (
        http_request_method
        + "\n"
        + canonical_uri
        + "\n"
        + canonical_querystring
        + "\n"
        + canonical_headers
        + "\n"
        + signed_headers
        + "\n"
        + hashed_request_payload
    )

    # ************* 步骤 2：拼接待签名字符串 *************
    credential_scope = f"{date}/{service}/tc3_request"
    hashed_canonical_request = hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()
    string_to_sign = algorithm + "\n" + str(timestamp) + "\n" + credential_scope + "\n" + hashed_canonical_request

    # ************* 步骤 3：计算签名 *************
    secret_date = _sign(f"TC3{secret_key}".encode("utf-8"), date)
    secret_service = _sign(secret_date, service)
    secret_signing = _sign(secret_service, "tc3_request")
    signature = hmac.new(secret_signing, string_to_sign.encode("utf-8"), hashlib.sha256).hexdigest()

    # ************* 步骤 4：拼接 Authorization *************
    authorization = (
        f"{algorithm} "
        f"Credential={secret_id}/{credential_scope}, "
        f"SignedHeaders={signed_headers}, "
        f"Signature={signature}"
    )

    # ************* 步骤 5：构造请求头 *************
    headers = {
        "Authorization": authorization,
        "Content-Type": "application/json; charset=utf-8",
        "Host": host,
        "X-TC-Action": action,
        "X-TC-Timestamp": str(timestamp),
        "X-TC-Version": version,
    }

    if region:
        headers["X-TC-Region"] = region
    if token:
        headers["X-TC-Token"] = token

    return headers


def detect_auth(id_card: str, name: str) -> Dict[str, Any]:
    """
    腾讯云FaceID身份认证接口

    用于发起身份认证流程，获取认证URL供用户进行人脸识别验证

    Args:
        id_card: 身份证号码
        name: 姓名

    Returns:
        包含API响应的字典，成功时包含认证URL等信息

    Raises:
        ValueError: 当必需参数缺失时抛出
        requests.RequestException: 当网络请求失败时抛出
    """
    # 获取配置参数
    rule_id = _get_config_value("tencent_faceid_rule_id")
    if rule_id is None:
        raise ValueError("rule_id参数缺失，请在gunicorn_config.py中配置tencent_faceid_rule_id")

    secret_id = _get_config_value("tencent_secret_id") or os.getenv("TENCENTCLOUD_SECRET_ID")
    if secret_id is None:
        raise ValueError("secret_id缺失，请在gunicorn_config.py或环境变量TENCENTCLOUD_SECRET_ID中配置")

    secret_key = _get_config_value("tencent_secret_key") or os.getenv("TENCENTCLOUD_SECRET_KEY")
    if secret_key is None:
        raise ValueError("secret_key缺失，请在gunicorn_config.py或环境变量TENCENTCLOUD_SECRET_KEY中配置")

    redirect_url = _get_config_value("tencent_faceid_redirect_url")
    if redirect_url is None:
        raise ValueError("redirect_url缺失，请在gunicorn_config.py中配置tencent_faceid_redirect_url")

    # 构建请求参数
    payload_data = {"RuleId": rule_id, "IdCard": id_card, "Name": name, "RedirectUrl": redirect_url}
    payload = json.dumps(payload_data)

    # 构建请求头
    headers = _build_tencent_headers("DetectAuth", payload, secret_id, secret_key)

    # 发送请求
    url = "https://faceid.tencentcloudapi.com/"
    try:
        response = requests.post(url=url, headers=headers, data=payload.encode("utf-8"), timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        raise requests.RequestException(f"腾讯云FaceID DetectAuth API请求失败: {e}")


def get_detect_info_enhanced(biz_token: str, info_type: str) -> Dict[str, Any]:
    """
    腾讯云FaceID获取检测信息增强版接口

    用于获取身份认证的详细结果信息

    Args:
        biz_token: 业务流水号，用于标识一次认证流程
        info_type: 信息类型，"1"表示获取全部信息

    Returns:
        包含API响应的字典，成功时包含认证结果详细信息

    Raises:
        ValueError: 当必需参数缺失时抛出
        requests.RequestException: 当网络请求失败时抛出
    """
    # 获取配置参数
    rule_id = _get_config_value("tencent_faceid_rule_id")
    if rule_id is None:
        raise ValueError("rule_id参数缺失，请在gunicorn_config.py中配置tencent_faceid_rule_id")

    secret_id = _get_config_value("tencent_secret_id") or os.getenv("TENCENTCLOUD_SECRET_ID")
    if secret_id is None:
        raise ValueError("secret_id缺失，请在gunicorn_config.py或环境变量TENCENTCLOUD_SECRET_ID中配置")

    secret_key = _get_config_value("tencent_secret_key") or os.getenv("TENCENTCLOUD_SECRET_KEY")
    if secret_key is None:
        raise ValueError("secret_key缺失，请在gunicorn_config.py或环境变量TENCENTCLOUD_SECRET_KEY中配置")

    # 构建请求参数
    payload_data = {"BizToken": biz_token, "RuleId": rule_id, "InfoType": info_type}
    payload = json.dumps(payload_data)

    # 构建请求头
    headers = _build_tencent_headers("GetDetectInfoEnhanced", payload, secret_id, secret_key)

    # 发送请求
    url = "https://faceid.tencentcloudapi.com/"
    try:
        response = requests.post(url=url, headers=headers, data=payload.encode("utf-8"), timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        raise requests.RequestException(f"腾讯云FaceID GetDetectInfoEnhanced API请求失败: {e}")


def process_face_auth_result(auth_token: str, biz_token: str) -> dict:
    """
    处理腾讯云人脸核身结果并提交到认证服务器

    完整流程：
    1. 调用腾讯云API获取核身结果
    2. 判断核身是否成功（ErrCode和Errcode都为0）
    3. 提取用户信息（姓名、身份证、时间等）
    4. 提交结果到认证服务器

    Args:
        auth_token (str): 认证令牌，用作请求头
        biz_token (str): 人脸核身业务标识

    Returns:
        dict: 处理结果的JSON响应
              成功时包含：real_name, id_card_number, biz_token, detect_auth_time, detect_auth_result
              失败时仅包含：biz_token

    Raises:
        ValueError: 当必需参数缺失时抛出
        requests.RequestException: 当网络请求失败时抛出
    """
    logger.info(f"开始处理人脸核身结果，biz_token: {biz_token}")

    try:
        # 步骤1：调用腾讯云API获取核身结果
        logger.info("调用腾讯云GetDetectInfoEnhanced API获取核身结果")
        tencent_response = get_detect_info_enhanced(biz_token, "1")

        # 检查腾讯云API响应格式
        if not isinstance(tencent_response, dict) or "Response" not in tencent_response:
            logger.error(f"腾讯云API返回格式异常: {tencent_response}")
            raise ValueError("腾讯云API响应格式异常")

        response = tencent_response["Response"]

        # 检查是否有错误信息
        if "Error" in response:
            error_info = response["Error"]
            logger.warning(f"腾讯云API返回错误: {error_info}")
            # API调用失败，返回仅包含biz_token的结果
            result_data = {"biz_token": biz_token}
            logger.info("腾讯云API调用失败，准备提交失败结果到认证服务器")
        else:
            # 步骤2：判断核身是否成功并提取数据
            text_data = response.get("Text", {})
            err_code = text_data.get("ErrCode")
            liveness_detail = text_data.get("LivenessDetail", [])

            # 判断核身成功条件：ErrCode为0且LivenessDetail中Errcode也为0
            detect_success = False
            if err_code == 0 and liveness_detail:
                first_detail = liveness_detail[0]
                if first_detail.get("Errcode") == 0:
                    detect_success = True

            logger.info(f"核身结果判断：ErrCode={err_code}, LivenessDetail.Errcode={liveness_detail[0].get('Errcode') if liveness_detail else 'N/A'}, 最终结果={detect_success}")

            if detect_success:
                # 步骤3：提取成功时的用户信息
                real_name = text_data.get("Name", "")
                id_card_number = text_data.get("IdCard", "")
                req_time = liveness_detail[0].get("ReqTime", "")

                # 步骤4：时间戳转换（毫秒转"YYYY-MM-DD HH:MM:SS"格式，使用Asia/Shanghai时区）
                detect_auth_time = ""
                if req_time:
                    try:
                        timestamp_ms = int(req_time)
                        # 创建Asia/Shanghai时区（UTC+8）
                        shanghai_tz = timezone(timedelta(hours=8))
                        # 先转换为UTC时间，然后转换为上海时区
                        dt_utc = datetime.fromtimestamp(timestamp_ms / 1000, timezone.utc)
                        dt_shanghai = dt_utc.astimezone(shanghai_tz)
                        detect_auth_time = dt_shanghai.strftime("%Y-%m-%d %H:%M:%S")
                        logger.info(f"时间转换成功（Asia/Shanghai时区）：{req_time} -> {detect_auth_time}")
                    except (ValueError, TypeError) as e:
                        logger.warning(f"时间戳转换失败: {req_time}, 错误: {e}")
                        detect_auth_time = ""

                # 构造成功时的完整数据
                result_data = {
                    "real_name": real_name,
                    "id_card_number": id_card_number,
                    "biz_token": biz_token,
                    "detect_auth_time": detect_auth_time,
                    "detect_auth_result": True
                }
                logger.info(f"核身成功，提取数据：姓名={real_name}, 身份证={id_card_number[:6]}****{id_card_number[-4:] if len(id_card_number) > 10 else '****'}")
            else:
                # 核身失败，构造失败数据
                result_data = {
                    "real_name": text_data.get("Name", ""),
                    "id_card_number": text_data.get("IdCard", ""),
                    "biz_token": biz_token,
                    "detect_auth_time": "",
                    "detect_auth_result": False
                }
                logger.info("核身失败，构造失败结果数据")

        # 步骤5：提交结果到认证服务器
        auth_server_url = f"{AUTH_SERVER_URL}/wechat/face_auth/"
        headers = {
            "Authorization": auth_token,
            "Content-Type": "application/json"
        }

        logger.info(f"准备提交结果到认证服务器: {auth_server_url}")
        logger.info(f"提交数据: {result_data}")

        # 发送POST请求到认证服务器
        auth_response = requests.post(
            auth_server_url,
            headers=headers,
            json=result_data,
            timeout=10
        )

        logger.info(f"认证服务器响应状态码: {auth_response.status_code}")

        # 返回提交的数据作为函数结果
        return result_data

    except requests.RequestException as e:
        logger.error(f"网络请求失败: {e}")
        # 网络异常时，返回仅包含biz_token的结果
        return {"biz_token": biz_token}
    except Exception as e:
        logger.error(f"处理人脸核身结果时发生未知错误: {e}")
        # 其他异常时，返回仅包含biz_token的结果
        return {"biz_token": biz_token}


if __name__ == "__main__":
    """
    调试和测试代码块

    使用说明：
    1. 在gunicorn_config.py的env字典中添加以下配置：
       "tencent_faceid_rule_id": "你的规则ID",
       "tencent_secret_id": "你的SecretId",
       "tencent_secret_key": "你的SecretKey"

    2. 或者设置环境变量：
       export TENCENTCLOUD_SECRET_ID=你的SecretId
       export TENCENTCLOUD_SECRET_KEY=你的SecretKey

    3. 运行此文件进行测试：
       python utils/tencent_faceid_utils.py
    """

    # 调试模式下的路径处理：确保能找到项目根目录的gunicorn_config.py
    try:
        import sys

        # 获取当前文件的目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 获取项目根目录（当前文件在utils/目录下，所以向上一级）
        project_root = os.path.dirname(current_dir)

        # 将项目根目录添加到sys.path（如果还没有的话）
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
            print(f"🔧 调试模式：已添加项目根目录到路径: {project_root}")
    except Exception as e:
        print(f"⚠️  路径处理警告: {e}")

    print("=" * 60)
    print("腾讯云FaceID工具函数调试测试")
    print("=" * 60)

    # 显示配置加载状态
    print("\n📋 配置加载状态:")
    print("-" * 30)

    # 检查配置文件是否可用
    config_loaded = False
    config_source = "未知"
    available_keys = []

    try:
        import gunicorn_config

        if hasattr(gunicorn_config, "env") and isinstance(gunicorn_config.env, dict):
            config_loaded = True
            config_source = "gunicorn_config.py"
            available_keys = list(gunicorn_config.env.keys())
    except (ImportError, AttributeError) as e:
        config_loaded = False
        config_source = f"导入失败: {e}"

    print(f"配置文件加载: {'✅ 成功' if config_loaded else '❌ 失败'}")
    print(f"配置来源: {config_source}")

    if config_loaded and available_keys:
        print(f"可用配置项: {', '.join(available_keys)}")
        # 检查腾讯云相关配置
        tencent_keys = [key for key in available_keys if "tencent" in key.lower()]
        if tencent_keys:
            print(f"腾讯云配置项: {', '.join(tencent_keys)}")
        else:
            print("⚠️  未找到腾讯云相关配置项")
    else:
        print("⚠️  将使用环境变量获取配置")

    print("-" * 30)

    while True:
        print("\n请选择要测试的函数:")
        print("1. detect_auth() - 身份认证接口")
        print("2. get_detect_info_enhanced() - 获取检测信息增强版接口")
        print("3. process_face_auth_result() - 处理人脸核身结果并提交到认证服务器")
        print("4. 退出")

        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == "1":
            print("\n" + "-" * 40)
            print("测试 detect_auth() 函数")
            print("-" * 40)

            try:
                # 获取用户输入参数
                id_card = input("请输入身份证号码: ").strip()
                if not id_card:
                    print("❌ 身份证号码不能为空")
                    continue

                name = input("请输入姓名: ").strip()
                if not name:
                    print("❌ 姓名不能为空")
                    continue

                print(f"\n调用参数:")
                print(f"  身份证号: {id_card}")
                print(f"  姓名: {name}")
                print("\n正在调用API...")

                result = detect_auth(id_card, name)
                print(f"✅ detect_auth() 调用成功")
                print(f"返回结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

            except ValueError as e:
                print(f"❌ 配置错误: {e}")
                print("请检查gunicorn_config.py中的配置或环境变量设置")
            except requests.RequestException as e:
                print(f"❌ 网络请求失败: {e}")
            except Exception as e:
                print(f"❌ 未知错误: {e}")

        elif choice == "2":
            print("\n" + "-" * 40)
            print("测试 get_detect_info_enhanced() 函数")
            print("-" * 40)

            try:
                # 获取用户输入参数
                biz_token = input("请输入业务Token: ").strip()
                if not biz_token:
                    print("❌ 业务Token不能为空")
                    continue

                info_type = input("请输入信息类型 (默认: 1): ").strip()
                if not info_type:
                    info_type = "1"

                print(f"\n调用参数:")
                print(f"  业务Token: {biz_token}")
                print(f"  信息类型: {info_type}")
                print("\n正在调用API...")

                result = get_detect_info_enhanced(biz_token, info_type)
                print(f"✅ get_detect_info_enhanced() 调用成功")
                print(f"返回结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

            except ValueError as e:
                print(f"❌ 配置错误: {e}")
                print("请检查gunicorn_config.py中的配置或环境变量设置")
            except requests.RequestException as e:
                print(f"❌ 网络请求失败: {e}")
            except Exception as e:
                print(f"❌ 未知错误: {e}")

        elif choice == "3":
            print("\n" + "-" * 40)
            print("测试 process_face_auth_result() 函数")
            print("-" * 40)

            try:
                # 获取用户输入参数
                auth_token = input("请输入认证令牌 (Authorization): ").strip()
                if not auth_token:
                    print("❌ 认证令牌不能为空")
                    continue

                biz_token = input("请输入业务Token: ").strip()
                if not biz_token:
                    print("❌ 业务Token不能为空")
                    continue

                print(f"\n调用参数:")
                print(f"  认证令牌: {auth_token[:20]}..." if len(auth_token) > 20 else f"  认证令牌: {auth_token}")
                print(f"  业务Token: {biz_token}")
                print("\n正在调用API...")

                result = process_face_auth_result(auth_token, biz_token)
                print(f"✅ process_face_auth_result() 调用成功")
                print(f"返回结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

            except ValueError as e:
                print(f"❌ 配置错误: {e}")
                print("请检查gunicorn_config.py中的配置或环境变量设置")
            except requests.RequestException as e:
                print(f"❌ 网络请求失败: {e}")
            except Exception as e:
                print(f"❌ 未知错误: {e}")

        elif choice == "4":
            print("\n感谢使用，再见！")
            break

        else:
            print("❌ 无效选择，请输入 1-4")

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
