# Generated by Django 4.1.13 on 2025-08-04 16:30

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('mediation_management', '0006_remove_mediationcase_mediation_agreement_and_more'),
        ('outbound_communication', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='smsrecord',
            name='creditor',
        ),
        migrations.RemoveField(
            model_name='smsrecord',
            name='debtor',
        ),
        migrations.AddField(
            model_name='smsrecord',
            name='mediation_case',
            field=models.ForeignKey(blank=True, help_text='短信关联的调解案件信息', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mediation_management.mediationcase', verbose_name='关联调解案件'),
        ),
        migrations.AlterField(
            model_name='smsrecord',
            name='sms_status',
            field=models.CharField(choices=[('sent_success', '发送成功'), ('sent_failed', '发送失败')], default='sent_success', help_text='短信发送的当前状态', max_length=20, verbose_name='发送状态'),
        ),
        migrations.AlterField(
            model_name='smsrecord',
            name='sms_type',
            field=models.CharField(choices=[('notification', '通知短信'), ('verification', '验证码短信'), ('reminder', '提醒短信'), ('other', '其他')], default='notification', help_text='短信的业务类型分类', max_length=20, verbose_name='短信类型'),
        ),
        migrations.AlterField(
            model_name='smsrecord',
            name='task_batch_id',
            field=models.CharField(blank=True, help_text='对应腾讯云API响应的RequestId', max_length=100, null=True, verbose_name='任务批次号'),
        ),
    ]
