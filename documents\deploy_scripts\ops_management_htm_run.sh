#!/bin/bash

# 本脚本用于更新容器，其功能是将旧的容器备份停止，并且启动新容器

# 确保脚本在遇到错误时停止
set -e

# 常量定义
IMAGE_TAR="ops_management_htm.tar"
IMAGE_NAME_PREFIX="ops_management_htm"
CONTAINER_NAME="ops-management-htm-container"
PORT="14010"
EXTERNAL_PATH="/file_server/htm/ops_management_htm"
CONFIG_PATH="$EXTERNAL_PATH/gunicorn_config.py"
UPLOAD_PATH="$EXTERNAL_PATH/upload"
LOG_PATH="$EXTERNAL_PATH/logs"
BACKUP_PATH="$EXTERNAL_PATH/backups"
TIMEZONE="Asia/Shanghai"

# 环境变量文件路径
ENV_FILE_PATH="$EXTERNAL_PATH/.env"

# 生成日期标签
TAG=$(date +%Y%m%d)
IMAGE_NAME="${IMAGE_NAME_PREFIX}:${TAG}"

# 函数：检查环境变量文件
check_env_file() {
    if [ ! -f "${ENV_FILE_PATH}" ]; then
        echo "❌ 错误：环境变量文件不存在: ${ENV_FILE_PATH}"
        echo "请将 .env.template 复制到 ${EXTERNAL_PATH}/.env 并配置敏感信息"
        echo "cp $(dirname "$0")/.env.template ${ENV_FILE_PATH}"
        exit 1
    fi
    echo "✅ 环境变量文件检查通过: ${ENV_FILE_PATH}"
}

# 函数：创建必要的目录
create_directories() {
    sudo mkdir -p "${EXTERNAL_PATH}"
    sudo mkdir -p "${UPLOAD_PATH}"
    sudo mkdir -p "${LOG_PATH}"
    sudo mkdir -p "${BACKUP_PATH}"
}

# 函数：检查并删除旧容器
check_and_remove_container() {
    if sudo docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        if sudo docker inspect -f '{{.State.Running}}' "${CONTAINER_NAME}" | grep -q "true"; then
            sudo docker stop "${CONTAINER_NAME}"
            echo "容器 ${CONTAINER_NAME} 已停止。"
        fi
        sudo docker rm "${CONTAINER_NAME}"
        echo "已删除包含 ${CONTAINER_NAME} 的容器。"
    fi
}

# 函数：判断并删除旧镜像
check_and_remove_image() {
    if sudo docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^${IMAGE_NAME}$"; then
        echo "镜像 ${IMAGE_NAME} 已存在，先删除该镜像。"
        sudo docker rmi "${IMAGE_NAME}"
    fi
}

# 函数：加载镜像
load_image() {
    sudo docker load -i ${IMAGE_TAR}
}

# 函数：启动容器
run_container() {
    echo "🚀 启动容器，使用环境变量文件: ${ENV_FILE_PATH}"
    sudo docker run -d \
        --name "${CONTAINER_NAME}" \
        -p "${PORT}:${PORT}" \
        -v "${CONFIG_PATH}:/app/gunicorn_config.py" \
        -v "${UPLOAD_PATH}:/app/upload" \
        -v "${LOG_PATH}:/app/logs" \
        -v "${BACKUP_PATH}:/app/backups" \
        --env-file "${ENV_FILE_PATH}" \
        -e TZ=${TIMEZONE} \
        "${IMAGE_NAME}"
}

# 函数：检查容器是否运行并显示日志
check_container_running() {
    sleep 10
    if sudo docker inspect -f '{{.State.Running}}' "${CONTAINER_NAME}" | grep -q "true"; then
        echo "以下是容器 ${CONTAINER_NAME} 的部分日志："
        sudo docker logs --tail 20 "${CONTAINER_NAME}"
        echo "容器 ${CONTAINER_NAME} 成功启动。"
    else
        echo "容器 ${CONTAINER_NAME} 未能成功启动。"
        exit 1
    fi
}

# 执行步骤
echo "🔍 开始容器部署流程..."
check_env_file
create_directories
check_and_remove_container
check_and_remove_image
load_image
run_container
check_container_running

echo "容器 ${CONTAINER_NAME} 已成功更新。"