#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : urls.py
<AUTHOR> JT_DA
@Date     : 2025/07/03
@File_Desc:
"""

from django.urls import path

from apps.user.views import auth_views, log_views, mediator_views, file_download_views

urlpatterns = [
    # 用户认证
    path("user_info/", auth_views.UserInfo.as_view()),
    # 刷新token
    path("token/", auth_views.GetNewAccessToken.as_view()),
    # 退出登录
    path("logout/", auth_views.UserLogout.as_view()),
    # 修改密码
    path("change_password/", auth_views.ChangePassword.as_view()),
    # 写入操作日志
    path("operation_log/", log_views.OperationLog.as_view()),
    # 调解员列表
    path("mediators/", mediator_views.MediatorListView.as_view()),
    # 安全文件下载接口 - 使用UUID安全标识符下载附件，防止路径暴露
    path("files/download/<uuid:secure_token>/", file_download_views.FileDownloadView.as_view(), name="secure_file_download"),
]
