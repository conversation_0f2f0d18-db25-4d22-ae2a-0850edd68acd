#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : tencent_sms_utils.py
<AUTHOR> JT_DA
@Date     : 2025/08/04
@File_Desc: 腾讯云SMS API工具函数
"""

import os
import hashlib
import hmac
import json
import time
import requests
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from django.utils import timezone as django_timezone

# 获取日志记录器
logger = logging.getLogger(__name__)


def _get_config_value(key: str, default: Any = None) -> Any:
    """
    从gunicorn_config.py配置文件中获取配置值

    Args:
        key: 配置键名
        default: 默认值

    Returns:
        配置值或默认值
    """
    try:
        import gunicorn_config

        return gunicorn_config.env.get(key, default)
    except (ImportError, AttributeError):
        return default


def _sign(key: bytes, msg: str) -> bytes:
    """
    使用HMAC-SHA256算法对消息进行签名

    Args:
        key: 签名密钥
        msg: 待签名的消息

    Returns:
        签名结果
    """
    return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()


def _build_tencent_headers(
    action: str,
    payload: str,
    secret_id: str,
    secret_key: str,
    service: str = "sms",
    version: str = "2021-01-11",
    region: str = "ap-guangzhou",
    token: str = "",
) -> Dict[str, str]:
    """
    构建腾讯云SMS API请求头

    Args:
        action: API动作名称
        payload: 请求体JSON字符串
        secret_id: 腾讯云SecretId
        secret_key: 腾讯云SecretKey
        service: 服务名称，默认为sms
        version: API版本，默认为2021-01-11
        region: 地域，默认为ap-guangzhou
        token: 临时凭证token，默认为空

    Returns:
        包含认证信息的请求头字典
    """
    host = f"{service}.tencentcloudapi.com"
    algorithm = "TC3-HMAC-SHA256"
    timestamp = int(time.time())
    date = datetime.fromtimestamp(timestamp, timezone.utc).strftime("%Y-%m-%d")

    # ************* 步骤 1：拼接规范请求串 *************
    http_request_method = "POST"
    canonical_uri = "/"
    canonical_querystring = ""
    ct = "application/json; charset=utf-8"
    canonical_headers = f"content-type:{ct}\nhost:{host}\nx-tc-action:{action.lower()}\n"
    signed_headers = "content-type;host;x-tc-action"
    hashed_request_payload = hashlib.sha256(payload.encode("utf-8")).hexdigest()
    canonical_request = (
        http_request_method
        + "\n"
        + canonical_uri
        + "\n"
        + canonical_querystring
        + "\n"
        + canonical_headers
        + "\n"
        + signed_headers
        + "\n"
        + hashed_request_payload
    )

    # ************* 步骤 2：拼接待签名字符串 *************
    credential_scope = f"{date}/{service}/tc3_request"
    hashed_canonical_request = hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()
    string_to_sign = algorithm + "\n" + str(timestamp) + "\n" + credential_scope + "\n" + hashed_canonical_request

    # ************* 步骤 3：计算签名 *************
    secret_date = _sign(f"TC3{secret_key}".encode("utf-8"), date)
    secret_service = _sign(secret_date, service)
    secret_signing = _sign(secret_service, "tc3_request")
    signature = hmac.new(secret_signing, string_to_sign.encode("utf-8"), hashlib.sha256).hexdigest()

    # ************* 步骤 4：拼接 Authorization *************
    authorization = (
        f"{algorithm} "
        f"Credential={secret_id}/{credential_scope}, "
        f"SignedHeaders={signed_headers}, "
        f"Signature={signature}"
    )

    # ************* 步骤 5：构造请求头 *************
    headers = {
        "Authorization": authorization,
        "Content-Type": "application/json; charset=utf-8",
        "Host": host,
        "X-TC-Action": action,
        "X-TC-Timestamp": str(timestamp),
        "X-TC-Version": version,
    }

    if region:
        headers["X-TC-Region"] = region
    if token:
        headers["X-TC-Token"] = token

    return headers


def send_sms(
    phone_numbers: List[str],
    template_id: str,
    template_params: Optional[List[str]] = None,
    sign_name: Optional[str] = None,
    sms_type: str = "notification",
    mediation_case_id: Optional[int] = None,
) -> Dict[str, Any]:
    """
    腾讯云SMS发送接口，发送后自动创建SmsRecord记录

    Args:
        phone_numbers: 手机号列表，支持单个或批量发送
        template_id: 模板ID，必须是已审核通过的模板
        template_params: 模板参数列表，可选
        sign_name: 短信签名，可选（优先使用传入值，否则使用配置默认值）
        sms_type: 短信类型，可选值：notification, verification, reminder, other
        mediation_case_id: 关联的调解案件ID，可选

    Returns:
        包含API响应和数据库记录创建结果的字典

    Raises:
        ValueError: 当必需参数缺失时抛出
        requests.RequestException: 当网络请求失败时抛出
    """
    # 参数验证
    if not phone_numbers:
        raise ValueError("手机号列表不能为空")
    
    if not template_id:
        raise ValueError("模板ID不能为空")

    # 验证短信类型
    valid_sms_types = ["notification", "verification", "reminder", "other"]
    if sms_type not in valid_sms_types:
        raise ValueError(f"短信类型无效，支持的类型：{valid_sms_types}")

    # 获取配置参数
    sdk_app_id = _get_config_value("tencent_sms_sdk_app_id")
    if sdk_app_id is None:
        raise ValueError("sdk_app_id参数缺失，请在gunicorn_config.py中配置tencent_sms_sdk_app_id")

    secret_id = _get_config_value("tencent_secret_id") or os.getenv("TENCENTCLOUD_SECRET_ID")
    if secret_id is None:
        raise ValueError("secret_id缺失，请在gunicorn_config.py或环境变量TENCENTCLOUD_SECRET_ID中配置")

    secret_key = _get_config_value("tencent_secret_key") or os.getenv("TENCENTCLOUD_SECRET_KEY")
    if secret_key is None:
        raise ValueError("secret_key缺失，请在gunicorn_config.py或环境变量TENCENTCLOUD_SECRET_KEY中配置")

    # 获取签名（优先使用传入值，否则使用配置默认值）
    if sign_name is None:
        sign_name = _get_config_value("tencent_sms_sign_name")
        if sign_name is None:
            raise ValueError("sign_name缺失，请传入签名参数或在gunicorn_config.py中配置tencent_sms_sign_name")

    # 构建请求参数
    payload_data = {
        "PhoneNumberSet": phone_numbers,
        "SmsSdkAppId": sdk_app_id,
        "TemplateId": template_id,
        "SignName": sign_name,
    }

    # 添加模板参数（如果提供）
    if template_params:
        payload_data["TemplateParamSet"] = template_params

    payload = json.dumps(payload_data)

    # 构建请求头
    headers = _build_tencent_headers("SendSms", payload, secret_id, secret_key)

    # 发送请求
    url = "https://sms.tencentcloudapi.com/"
    try:
        logger.info(f"开始发送短信，目标手机号：{phone_numbers}, 模板ID：{template_id}")
        response = requests.post(url=url, headers=headers, data=payload.encode("utf-8"), timeout=30)
        response.raise_for_status()
        api_response = response.json()
        
        logger.info(f"腾讯云SMS API调用成功，RequestId：{api_response.get('Response', {}).get('RequestId')}")
        
        # 发送成功后创建SmsRecord记录
        _create_sms_records(
            api_response=api_response,
            phone_numbers=phone_numbers,
            sms_content=_build_sms_content(template_id, template_params, sign_name),
            sms_type=sms_type,
            mediation_case_id=mediation_case_id,
        )
        
        return api_response
        
    except requests.exceptions.RequestException as e:
        logger.error(f"腾讯云SMS API请求失败: {e}")
        
        # 请求失败时也创建失败记录
        _create_failed_sms_records(
            phone_numbers=phone_numbers,
            sms_content=_build_sms_content(template_id, template_params, sign_name),
            sms_type=sms_type,
            failure_reason=str(e),
            mediation_case_id=mediation_case_id,
        )
        
        raise requests.RequestException(f"腾讯云SMS API请求失败: {e}")


def _build_sms_content(template_id: str, template_params: Optional[List[str]], sign_name: str) -> str:
    """
    构建短信内容用于记录存储
    
    Args:
        template_id: 模板ID
        template_params: 模板参数
        sign_name: 短信签名
        
    Returns:
        构建的短信内容字符串
    """
    content_parts = [f"【{sign_name}】"]
    content_parts.append(f"模板ID：{template_id}")
    
    if template_params:
        content_parts.append(f"参数：{', '.join(template_params)}")
    
    return " ".join(content_parts)


def _create_sms_records(
    api_response: Dict[str, Any],
    phone_numbers: List[str],
    sms_content: str,
    sms_type: str,
    mediation_case_id: Optional[int],
) -> None:
    """
    根据API响应创建SMS记录
    
    Args:
        api_response: 腾讯云API响应
        phone_numbers: 手机号列表
        sms_content: 短信内容
        sms_type: 短信类型
        mediation_case_id: 调解案件ID
    """
    try:
        # 导入Django模型（延迟导入避免循环依赖）
        from apps.outbound_communication.models import SmsRecord
        from apps.mediation_management.models import MediationCase
        
        response_data = api_response.get("Response", {})
        request_id = response_data.get("RequestId")
        send_status_set = response_data.get("SendStatusSet", [])
        
        # 获取调解案件对象
        mediation_case = None
        if mediation_case_id:
            try:
                mediation_case = MediationCase.objects.get(id=mediation_case_id)
            except MediationCase.DoesNotExist:
                logger.warning(f"调解案件ID {mediation_case_id} 不存在")
        
        # 为每个手机号创建记录
        for i, phone_number in enumerate(phone_numbers):
            # 获取对应的发送状态
            send_status = None
            if i < len(send_status_set):
                send_status = send_status_set[i]
            
            # 确定发送状态
            if send_status and send_status.get("Code") == "Ok":
                sms_status = "sent_success"
                failure_reason = None
                send_time = django_timezone.now()
                delivery_time = django_timezone.now()  # 腾讯云返回成功即表示送达
            else:
                sms_status = "sent_failed"
                failure_reason = send_status.get("Message") if send_status else "未知错误"
                send_time = django_timezone.now()
                delivery_time = None
            
            # 创建SMS记录
            sms_record = SmsRecord.objects.create(
                recipient_phone=phone_number,
                sms_content=sms_content,
                sms_status=sms_status,
                sms_type=sms_type,
                send_time=send_time,
                delivery_time=delivery_time,
                task_batch_id=request_id,  # 使用RequestId作为批次号
                mediation_case=mediation_case,
                failure_reason=failure_reason,
            )
            
            logger.info(f"SMS记录创建成功，ID：{sms_record.id}, 手机号：{phone_number}, 状态：{sms_status}")
            
    except Exception as e:
        logger.error(f"创建SMS记录失败: {e}")


def _create_failed_sms_records(
    phone_numbers: List[str],
    sms_content: str,
    sms_type: str,
    failure_reason: str,
    mediation_case_id: Optional[int],
) -> None:
    """
    创建失败的SMS记录
    
    Args:
        phone_numbers: 手机号列表
        sms_content: 短信内容
        sms_type: 短信类型
        failure_reason: 失败原因
        mediation_case_id: 调解案件ID
    """
    try:
        # 导入Django模型（延迟导入避免循环依赖）
        from apps.outbound_communication.models import SmsRecord
        from apps.mediation_management.models import MediationCase
        
        # 获取调解案件对象
        mediation_case = None
        if mediation_case_id:
            try:
                mediation_case = MediationCase.objects.get(id=mediation_case_id)
            except MediationCase.DoesNotExist:
                logger.warning(f"调解案件ID {mediation_case_id} 不存在")
        
        # 为每个手机号创建失败记录
        for phone_number in phone_numbers:
            sms_record = SmsRecord.objects.create(
                recipient_phone=phone_number,
                sms_content=sms_content,
                sms_status="sent_failed",
                sms_type=sms_type,
                send_time=django_timezone.now(),
                delivery_time=None,
                task_batch_id=None,  # 请求失败时没有RequestId
                mediation_case=mediation_case,
                failure_reason=failure_reason,
            )
            
            logger.info(f"SMS失败记录创建成功，ID：{sms_record.id}, 手机号：{phone_number}")
            
    except Exception as e:
        logger.error(f"创建SMS失败记录失败: {e}")


if __name__ == "__main__":
    """
    调试和测试代码块

    使用说明：
    1. 在gunicorn_config.py的env字典中添加以下配置：
       "tencent_sms_sdk_app_id": "你的SMS应用ID",
       "tencent_secret_id": "你的SecretId",
       "tencent_secret_key": "你的SecretKey",
       "tencent_sms_sign_name": "你的短信签名"

    2. 或者设置环境变量：
       export TENCENTCLOUD_SECRET_ID=你的SecretId
       export TENCENTCLOUD_SECRET_KEY=你的SecretKey

    3. 运行此文件进行测试：
       python utils/tencent_sms_utils.py
    """

    # 调试模式下的路径处理：确保能找到项目根目录的gunicorn_config.py
    try:
        import sys
        import django

        # 获取当前文件的目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 获取项目根目录（当前文件在utils/目录下，所以向上一级）
        project_root = os.path.dirname(current_dir)

        # 将项目根目录添加到sys.path（如果还没有的话）
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
            print(f"🔧 调试模式：已添加项目根目录到路径: {project_root}")

        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
        django.setup()
        print("🔧 Django环境初始化成功")

    except Exception as e:
        print(f"⚠️  环境初始化警告: {e}")

    print("=" * 60)
    print("腾讯云SMS工具函数调试测试")
    print("=" * 60)

    # 显示配置加载状态
    print("\n📋 配置加载状态:")
    print("-" * 30)

    # 检查配置文件是否可用
    config_loaded = False
    config_source = "未知"
    available_keys = []

    try:
        import gunicorn_config

        if hasattr(gunicorn_config, "env") and isinstance(gunicorn_config.env, dict):
            config_loaded = True
            config_source = "gunicorn_config.py"
            available_keys = list(gunicorn_config.env.keys())
    except (ImportError, AttributeError) as e:
        config_loaded = False
        config_source = f"导入失败: {e}"

    print(f"配置文件加载: {'✅ 成功' if config_loaded else '❌ 失败'}")
    print(f"配置来源: {config_source}")

    if config_loaded and available_keys:
        print(f"可用配置项: {', '.join(available_keys)}")
        # 检查腾讯云SMS相关配置
        sms_keys = [key for key in available_keys if "sms" in key.lower() or "tencent" in key.lower()]
        if sms_keys:
            print(f"腾讯云SMS配置项: {', '.join(sms_keys)}")
        else:
            print("⚠️  未找到腾讯云SMS相关配置项")
    else:
        print("⚠️  将使用环境变量获取配置")

    print("-" * 30)

    while True:
        print("\n请选择要测试的功能:")
        print("1. send_sms() - 发送短信接口")
        print("2. 查看配置信息")
        print("3. 退出")

        choice = input("\n请输入选择 (1-3): ").strip()

        if choice == "1":
            print("\n" + "-" * 40)
            print("测试 send_sms() 函数")
            print("-" * 40)

            try:
                # 获取用户输入参数
                phone_input = input("请输入手机号（多个用逗号分隔）: ").strip()
                if not phone_input:
                    print("❌ 手机号不能为空")
                    continue

                phone_numbers = [phone.strip() for phone in phone_input.split(",")]

                template_id = input("请输入模板ID: ").strip()
                if not template_id:
                    print("❌ 模板ID不能为空")
                    continue

                template_params_input = input("请输入模板参数（多个用逗号分隔，可选）: ").strip()
                template_params = None
                if template_params_input:
                    template_params = [param.strip() for param in template_params_input.split(",")]

                sign_name = input("请输入短信签名（可选，留空使用配置默认值）: ").strip()
                if not sign_name:
                    sign_name = None

                sms_type = input("请输入短信类型 (notification/verification/reminder/other，默认: notification): ").strip()
                if not sms_type:
                    sms_type = "notification"

                mediation_case_id_input = input("请输入调解案件ID（可选）: ").strip()
                mediation_case_id = None
                if mediation_case_id_input:
                    try:
                        mediation_case_id = int(mediation_case_id_input)
                    except ValueError:
                        print("⚠️  调解案件ID格式错误，将忽略")

                print(f"\n调用参数:")
                print(f"  手机号: {phone_numbers}")
                print(f"  模板ID: {template_id}")
                print(f"  模板参数: {template_params}")
                print(f"  短信签名: {sign_name or '使用配置默认值'}")
                print(f"  短信类型: {sms_type}")
                print(f"  调解案件ID: {mediation_case_id}")
                print("\n正在调用API...")

                result = send_sms(
                    phone_numbers=phone_numbers,
                    template_id=template_id,
                    template_params=template_params,
                    sign_name=sign_name,
                    sms_type=sms_type,
                    mediation_case_id=mediation_case_id,
                )
                print(f"✅ send_sms() 调用成功")
                print(f"返回结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

            except ValueError as e:
                print(f"❌ 配置错误: {e}")
                print("请检查gunicorn_config.py中的配置或环境变量设置")
            except requests.RequestException as e:
                print(f"❌ 网络请求失败: {e}")
            except Exception as e:
                print(f"❌ 未知错误: {e}")

        elif choice == "2":
            print("\n" + "-" * 40)
            print("配置信息详情")
            print("-" * 40)

            # 显示环境变量
            print("环境变量:")
            env_vars = ["TENCENTCLOUD_SECRET_ID", "TENCENTCLOUD_SECRET_KEY"]
            for var in env_vars:
                value = os.getenv(var)
                if value:
                    print(f"  {var}: {'*' * 8}...{value[-4:] if len(value) > 4 else '****'}")
                else:
                    print(f"  {var}: 未设置")

            # 显示配置文件信息
            print("\n配置文件信息:")
            if config_loaded:
                config_keys = ["tencent_sms_sdk_app_id", "tencent_secret_id", "tencent_secret_key", "tencent_sms_sign_name"]
                for key in config_keys:
                    value = _get_config_value(key)
                    if value:
                        if "secret" in key.lower():
                            print(f"  {key}: {'*' * 8}...{str(value)[-4:] if len(str(value)) > 4 else '****'}")
                        else:
                            print(f"  {key}: {value}")
                    else:
                        print(f"  {key}: 未配置")
            else:
                print("  配置文件加载失败")

        elif choice == "3":
            print("👋 退出测试程序")
            break

        else:
            print("❌ 无效选择，请重新输入")
