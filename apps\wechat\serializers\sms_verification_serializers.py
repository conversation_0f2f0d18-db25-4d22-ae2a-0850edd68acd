#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : sms_verification_serializers.py
<AUTHOR> JT_DA
@Date     : 2025/08/05
@File_Desc: 短信验证码相关序列化器
"""

import re
from rest_framework import serializers


class SMSVerificationSerializer(serializers.Serializer):
    """短信验证码发送序列化器 - 用于验证发送短信验证码的请求参数"""

    # 手机号码 - 接收验证码的手机号码
    phone_number = serializers.CharField(
        max_length=11,
        min_length=11,
        required=True,
        help_text="接收验证码的手机号码，必须为11位数字"
    )

    def validate_phone_number(self, value):
        """
        验证手机号码格式
        
        Args:
            value: 手机号码字符串
            
        Returns:
            str: 验证通过的手机号码
            
        Raises:
            ValidationError: 当手机号码格式不正确时抛出
        """
        # 验证手机号码格式：11位数字，以1开头
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, value):
            raise serializers.ValidationError("手机号码格式不正确，请输入有效的11位手机号码")
        
        return value


class ContactPhoneUpdateSerializer(serializers.Serializer):
    """联系号码更新序列化器 - 用于验证更新调解案件联系号码的请求参数"""

    # 手机号码 - 要更新的联系手机号码
    phone_number = serializers.CharField(
        max_length=11,
        min_length=11,
        required=True,
        help_text="要更新的联系手机号码，必须为11位数字"
    )

    # 短信验证码 - 用于验证手机号码所有权的验证码
    sms_code = serializers.CharField(
        max_length=6,
        min_length=6,
        required=True,
        help_text="短信验证码，必须为6位数字"
    )

    def validate_phone_number(self, value):
        """
        验证手机号码格式
        
        Args:
            value: 手机号码字符串
            
        Returns:
            str: 验证通过的手机号码
            
        Raises:
            ValidationError: 当手机号码格式不正确时抛出
        """
        # 验证手机号码格式：11位数字，以1开头
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, value):
            raise serializers.ValidationError("手机号码格式不正确，请输入有效的11位手机号码")
        
        return value

    def validate_sms_code(self, value):
        """
        验证短信验证码格式
        
        Args:
            value: 验证码字符串
            
        Returns:
            str: 验证通过的验证码
            
        Raises:
            ValidationError: 当验证码格式不正确时抛出
        """
        # 验证验证码格式：6位数字
        if not value.isdigit():
            raise serializers.ValidationError("验证码必须为6位数字")
        
        return value
