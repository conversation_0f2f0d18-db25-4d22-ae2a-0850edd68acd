#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : celery.py
<AUTHOR> JT_DA
@Date     : 2025/07/01
@File_Desc:
"""

import os

from celery import Celery
from celery.schedules import crontab
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ops_management.settings")

app = Celery("ops_management_tasks")

app.config_from_object("django.conf:settings", namespace="CELERY")

# Load task modules from all registered Django apps.
app.autodiscover_tasks(["ops_management.times"])
app.autodiscover_tasks()

app.conf.update(
    CELERYD_CONCURRENCY=settings.CELERYD_CONCURRENCY,
    CELERYD_MAX_TASKS_PER_CHILD=settings.CELERYD_MAX_TASKS_PER_CHILD,
)

app.conf.update(
    CELERYBEAT_SCHEDULE={
        "clear_token": {
            "task": "ops_management.times.clear_token",
            "schedule": crontab(hour="4", minute="2"),
        },
        "auto_backup_database": {
            "task": "ops_management.times.auto_backup_database",
            "schedule": crontab(hour="4", minute="12"),
        },
        "sync_auth_user_local": {
            "task": "apps.user.tasks.sync_auth_user_local",
            # 每15分钟执行一次
            "schedule": crontab(minute="*/15"),
        },
        "sync_auth_group_local": {
            "task": "apps.user.tasks.sync_auth_group_local",
            # 每15分钟执行一次
            "schedule": crontab(minute="*/15"),
        },
    }
)


class CeleryResult:
    def __init__(self, task_name, status, error=None):
        self.task_name = task_name
        self.status = status
        self.error = error

    @classmethod
    def success(cls, task_name):
        return cls(task_name, "success").to_dict()

    @classmethod
    def fail(cls, task_name, error):
        return cls(task_name, "fail", error=error).to_dict()

    def to_dict(self):
        return {
            "task_name": self.task_name,
            "status": self.status,
            "error": self.error,
        }
