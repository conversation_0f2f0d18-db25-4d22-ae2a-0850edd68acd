#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : log_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/03
@File_Desc:
"""

import requests
from ipware import get_client_ip
from rest_framework.views import APIView

from ops_management.settings import AUTH_SERVER_URL, SYSTEM_NAME
from utils.ajax_result import AjaxResult
from utils.permission_helper import MyPermission, WechatFaceAuthPermission
from utils.user_helper import request_auth_user_info


class OperationLog(APIView):
    """
    操作日志记录视图

    记录用户在系统中的操作行为，包括功能按钮点击、页面访问等操作信息。
    将操作日志发送到认证服务器进行统一存储和管理，用于系统审计和行为分析。
    """

    permission_classes = [MyPermission | WechatFaceAuthPermission]

    def post(self, request):
        """
        记录用户操作日志

        接收前端发送的用户操作信息，包括按钮名称、操作类型、页面URL等，
        结合用户身份信息和客户端IP地址，发送到认证服务器进行日志记录。

        **请求参数：**
        **请求头参数：**
        - Authorization (字符串, 必需): 用户访问令牌，格式为 "Bearer token_string"

        **请求体参数：**
        - button_name (字符串, 可选): 功能按钮名称，如 "保存"、"删除"、"查询"
        - button_type (字符串, 可选): 功能按钮类型，如 "submit"、"query"、"delete"
        - page_url (字符串, 可选): 页面URL地址，记录操作发生的页面位置
        - page_plate (字符串, 可选): 页面所属版块，如 "用户管理"、"数据治理"

        **请求数据示例：**
        ```json
        {
            "button_name": "保存",
            "button_type": "submit",
            "page_url": "/user/profile",
            "page_plate": "用户管理"
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "state": "success",
            "data": null
        }
        ```

        **错误响应示例：**
        ```json
        {
            "code": 500,
            "msg": "操作失败",
            "state": "fail",
            "data": null
        }
        ```
        """
        # 对接用户系统
        user_info = request_auth_user_info(request)
        username = user_info.get("username")
        # real_name = user_info.get("real_name")

        # 1.获取参数
        button_name = request.data.get("button_name", None)  # 功能按钮名称
        button_type = request.data.get("button_type", None)  # 功能按钮类型
        page_url = request.data.get("page_url", None)  # 页面url
        page_plate = request.data.get("page_plate", None)  # 页面所属版块

        client_ip, _ = get_client_ip(request)
        try:
            auth_header = request.headers.get("Authorization")
            data = {
                "username": username,
                "account": username,
                "button": button_name,
                "kind": button_type,
                "url": page_url,
                "belong": f"{SYSTEM_NAME}：" + page_plate,
                "ip": client_ip,
            }
            url = f"{AUTH_SERVER_URL}/log/operation_log/"
            headers = {"Authorization": auth_header}
            _ = requests.request("POST", url, headers=headers, data=data)
            return AjaxResult.success()
        except Exception as e:
            return AjaxResult.fail()
