#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : user_helper.py
<AUTHOR> JT_DA
@Date     : 2025/07/03
@File_Desc:
"""


import requests

from ops_management.settings import AUTH_SERVER_URL, SYSTEM_NAME


def request_auth_user_info(request, auth_token=None):
    """
    获取认证用户信息（含安全性增强）

    Args:
        request: HTTP请求对象
        auth_token: 可选的认证令牌，如果不提供则从请求头获取

    Returns:
        dict: 用户信息字典
              - 微信用户（username以"wx_"开头）：仅返回安全字段（user_id, username,
                wechat_openid, wechat_nickname, wechat_avatar_url, detect_auth_result）
              - 非微信用户：返回完整用户信息（包含角色、部门、权限等）
              - 如果获取失败则返回空字典
    """
    # 获取认证头信息，优先使用传入的auth_token
    auth_header = auth_token if auth_token else request.headers.get("Authorization")

    if not auth_header:
        return {}

    try:
        # 向认证服务器请求用户信息
        response = requests.get(f"{AUTH_SERVER_URL}/info/", headers={"Authorization": auth_header})

        # 检查响应状态码
        if response.status_code != 200:
            return {}

        # 解析响应JSON数据
        response_data = response.json()
        if not response_data or not isinstance(response_data, dict):
            return {}

        # 获取用户数据，添加空值检查
        auth_system_user_info = response_data.get("data")
        if not auth_system_user_info or not isinstance(auth_system_user_info, dict):
            return {}

        # 安全获取角色信息，避免AttributeError
        role = {}
        role_list = auth_system_user_info.get("role")
        if role_list and isinstance(role_list, list) and len(role_list) > 0:
            role = role_list[0] if isinstance(role_list[0], dict) else {}

        # 安全获取部门信息，避免AttributeError
        group = {}
        department_list = auth_system_user_info.get("department")
        if department_list and isinstance(department_list, list) and len(department_list) > 0:
            group = department_list[0] if isinstance(department_list[0], dict) else {}

        # 安全获取用户权限信息
        user_permissions = []
        permissions_data = auth_system_user_info.get("user_all_permissions")
        if permissions_data and isinstance(permissions_data, dict):
            system_permissions = permissions_data.get(f"{SYSTEM_NAME}")
            if system_permissions and isinstance(system_permissions, list):
                user_permissions = system_permissions

        # 获取用户名用于安全判断
        username = auth_system_user_info.get("username", "")

        # 安全性增强：对微信用户进行信息过滤
        # 微信用户（username以"wx_"开头）仅返回必要的身份信息，避免敏感数据泄露
        if username.startswith("wx_"):
            # 微信用户仅返回安全字段，不包含角色、部门、权限等敏感信息
            real_name = auth_system_user_info.get("real_name")
            user_info = {
                "user_id": auth_system_user_info.get("id"),
                "username": username,
                "wechat_openid": auth_system_user_info.get("wechat_openid"),
                "wechat_nickname": real_name if real_name else auth_system_user_info.get("wechat_nickname"),
                "wechat_avatar_url": auth_system_user_info.get("wechat_avatar_url"),
                "detect_auth_result": auth_system_user_info.get("detect_auth_result"),
            }
        else:
            # 非微信用户返回完整用户信息
            user_info = {
                # 核心用户标识信息 - 最高优先级
                "user_id": auth_system_user_info.get("id"),
                "username": username,
                # 基本用户信息 - 高优先级
                "real_name": auth_system_user_info.get("real_name"),
                "is_staff": auth_system_user_info.get("is_staff"),
                # 角色信息 - 中高优先级
                "role_id": role.get("id", "") if isinstance(role, dict) else "",
                "role_name": role.get("name", "") if isinstance(role, dict) else "",
                # 部门信息 - 中优先级
                "group_id": group.get("id", "") if isinstance(group, dict) else "",
                "group_name": group.get("name", "") if isinstance(group, dict) else "",
                # 权限信息 - 中优先级
                "user_all_permissions": user_permissions,
                # 微信相关信息 - 低优先级
                "wechat_openid": auth_system_user_info.get("wechat_openid"),
                "wechat_nickname": auth_system_user_info.get("wechat_nickname"),
                "wechat_avatar_url": auth_system_user_info.get("wechat_avatar_url"),
                # 其他扩展信息 - 最低优先级
                "detect_auth_result": auth_system_user_info.get("detect_auth_result"),
            }

        return user_info

    except requests.exceptions.RequestException:
        # 网络请求异常处理
        return {}
    except (ValueError, TypeError, KeyError):
        # JSON解析异常或数据类型异常处理
        return {}
    except Exception:
        # 其他未预期异常的兜底处理
        return {}
