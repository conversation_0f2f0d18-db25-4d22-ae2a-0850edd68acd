# 腾讯云API密钥安全配置指南

## 🔒 安全部署步骤

### 1. 创建环境变量文件
```bash
# 复制模板到外部挂载路径
cp documents/deploy_scripts/.env.template /file_server/htm/ops_management_htm/.env
```

### 2. 配置腾讯云API密钥
编辑 `/file_server/htm/ops_management_htm/.env` 文件，填入真实的腾讯云API密钥：

```bash
# 腾讯云API密钥（从腾讯云控制台获取）
TENCENTCLOUD_SECRET_ID=AKID...
TENCENTCLOUD_SECRET_KEY=...
```

### 3. 设置文件权限
```bash
chmod 600 /file_server/htm/ops_management_htm/.env  # 仅所有者可读写
```

### 4. 运行部署脚本
```bash
./documents/deploy_scripts/ops_management_htm_run.sh
```

## 🛡️ 安全最佳实践

### 文件权限管理
- `.env` 文件权限设置为 600 (仅所有者可读写)
- 定期轮换腾讯云API密钥
- 使用不同环境的不同密钥

### 版本控制安全
- `.env` 文件位于外部挂载路径，不在代码仓库中
- 仅提交 `.env.template` 模板文件
- 团队成员各自维护自己的 `.env` 文件

### 生产环境建议
- 定期轮换腾讯云API密钥
- 启用腾讯云API访问日志监控
- 设置API调用频率限制
- 考虑使用腾讯云CAM角色进行更细粒度的权限控制

## 🔧 故障排除

### 常见错误
1. **环境变量文件不存在**
   - 确保已复制 `.env.template` 到 `/file_server/htm/ops_management_htm/.env`

2. **权限被拒绝**
   - 检查 `.env` 文件权限：`ls -la /file_server/htm/ops_management_htm/.env`
   - 设置正确权限：`chmod 600 /file_server/htm/ops_management_htm/.env`

3. **容器启动失败**
   - 检查腾讯云API密钥是否正确配置
   - 查看容器日志：`docker logs ops-management-htm-container`

### 验证配置
```bash
# 检查环境变量是否正确传递到容器
docker exec ops-management-htm-container env | grep TENCENTCLOUD
```

## ⚠️ 重要提醒

- **绝不要**将包含真实密钥的 `.env` 文件提交到版本控制系统
- **绝不要**在日志中输出腾讯云API密钥
- **定期**更新和轮换腾讯云API密钥
- **监控**腾讯云API调用和访问模式
